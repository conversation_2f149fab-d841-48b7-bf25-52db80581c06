{"flutter": {"platforms": {"android": {"default": {"projectId": "hodan-medical-app", "appId": "1:59552903675:android:c8b5fb9abf349224f25b5e", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "hodan-medical-app", "appId": "1:59552903675:ios:6790f1cb94d29a3cf25b5e", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "macos": {"default": {"projectId": "hodan-medical-app", "appId": "1:59552903675:ios:e9b89d1d0a9b2c85f25b5e", "uploadDebugSymbols": false, "fileOutput": "macos/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "hodan-medical-app", "configurations": {"android": "1:59552903675:android:c8b5fb9abf349224f25b5e", "ios": "1:59552903675:ios:6790f1cb94d29a3cf25b5e", "macos": "1:59552903675:ios:e9b89d1d0a9b2c85f25b5e", "web": "1:59552903675:web:4d0d567ae0985fe6f25b5e", "windows": "1:59552903675:web:c311e66d229c2a9ff25b5e"}}}}}}