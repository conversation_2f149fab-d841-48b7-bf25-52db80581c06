// import 'dart:io';

import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hodan_hospital/app.dart';
import 'package:hodan_hospital/core/config/di/dependency_injection.dart' as di;
import 'package:hodan_hospital/core/config/di/dependency_injection.dart';
import 'package:hodan_hospital/core/config/enviroment/enviroment_config.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';

import 'core/network/ssl validation/app_http_overrides.dart';
import 'core/services/analytics_service.dart';
import 'core/services/crashlytics_service.dart';
import 'core/services/notification_services.dart';
import 'features/shared/presentation/pages/modern_error_page.dart';
import 'firebase_options.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  /// Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  /// setup custom SSL validation
  HttpOverrides.global = AppHttpOverrides();

  /// setup logger
  final AppLogger appLogger = AppLogger();
  await appLogger.setupLogging();

  /// Load environment config
  await EnvironmentConfig.loadEnv();

  /// Registering dependencies
  await di.setupServiceLocator();

  ///
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  final notificationService = sl<NotificationService>();
  await notificationService.initialize();
  await notificationService.requestPermissions();
  final token = await notificationService.getToken();
  if (token != null) {
    appLogger.info('FCM Token: $token');
  }

  await setupErrorHandling();

  /// Run the app
  runApp(const MyApp());
}

Future<void> setupErrorHandling() async {
  final analyticsService = sl<AnalyticsService>();
  final crashlyticsService = sl<CrashlyticsService>();

  /// Handle errors gracefully
  ErrorWidget.builder = (FlutterErrorDetails details) {
    analyticsService.logError(
      error: details.exceptionAsString(),
      stackTrace: details.stack,
      context: 'ErrorWidget.builder',
    );
    return ModernErrorPage(details: details);
  };

  FlutterError.onError = (FlutterErrorDetails details) async {
    await Future.wait([
      analyticsService.logError(
        error: details.exceptionAsString(),
        stackTrace: details.stack,
        context: 'FlutterError.onError',
      ),
      crashlyticsService.recordFlutterFatalError(details),
    ]);
  };

  PlatformDispatcher.instance.onError = (Object error, StackTrace stack) {
    analyticsService.logError(
      error: error.toString(),
      stackTrace: stack,
      context: 'PlatformDispatcher.onError',
    );
    crashlyticsService.logError(
      error: error.toString(),
      stackTrace: stack,
      context: 'PlatformDispatcher.onError',
      fatal: true,
      additionalInfo: {
        'error_type': error.runtimeType.toString(),
        'error_message': error.toString(),
        'stack_trace': stack.toString(),
      },
    );
    return true; // prevent app from crashing
  };
}
