import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';

class EnvironmentConfig {
  EnvironmentConfig._(); // Prevent instantiation

  /// Load environment variables from .env file
  static Future<void> loadEnv({bool forceProduction = true}) async {
    try {
      final isProduction = const bool.fromEnvironment('dart.vm.product');
      AppLogger().info(
        'Environment: ${isProduction ? 'Production' : 'Development'}',
      );

      // final envFile = forceProduction || isProduction
      //     ? '.env.production'
      //     : '.env.development';
      final envFile = '.env.production';
      AppLogger().info(
        'Loading environment variables from: $envFile',
      );

      await dotenv.load(fileName: envFile);

      AppLogger()
          .info('Environment variables loaded successfully from : $envFile');
    } catch (error, stackTrace) {
      AppLogger().error(
        'Failed to load environment variables ${error.toString()}',
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  static String _getEnvVariable(String key, {String defaultValue = ''}) {
    try {
      final value = dotenv.env[key];
      if (value == null || value.isEmpty) {
        AppLogger().error(
            'Environment variable "$key" is missing, using default value.');
        return defaultValue;
      }
      return value;
    } catch (error, stackTrace) {
      AppLogger().error(
        'Failed to get environment variable "$key"',
        error: error,
        stackTrace: stackTrace, 
      );
      return defaultValue;
    }
  }

  static String get currentEnv => _getEnvVariable('CURRENT_ENV');
  static String get baseUrl => _getEnvVariable('BASE_URL');
  // static String get _apiKey => _getEnvVariable('API_KEY');
  // static String get _apiSecret => _getEnvVariable('API_SECRET');
  // static String get token => 'token $_apiKey:$_apiSecret';
  static String get apiToken => _getEnvVariable('API_TOKEN');
  static String get emailUsername => _getEnvVariable('EMAIL_USERNAME');
  static String get emailPassword => _getEnvVariable('EMAIL_PASSWORD');
  static String get hodanMerchantUid => _getEnvVariable('HODAN_MERCHANT_UID');
  static String get hodanMerchantApiUserId =>
      _getEnvVariable('HODAN_MERCHANT_API_USER_ID');
  static String get hodanMerchantApiKey =>
      _getEnvVariable('HODAN_MERCHANT_API_KEY');
  static String get hodanMerchantApiUrl =>
      _getEnvVariable('HODAN_MERCHANT_API_URL');

  static String get rasiinMerchantUid => _getEnvVariable('RASIIN_MERCHANT_UID');
  static String get rasiinMerchantApiUserId =>
      _getEnvVariable('RASIIN_MERCHANT_API_USER_ID');
  static String get rasiinMerchantApiKey =>
      _getEnvVariable('RASIIN_MERCHANT_API_KEY');
  static String get rasiinMerchantApiUrl =>
      _getEnvVariable('RASIIN_MERCHANT_API_URL');

  /// SMS Related
  static String get smsBaseUrl => _getEnvVariable('SMS_BASE_URL');
  static String get smsUsername => _getEnvVariable('SMS_USERNAME');
  static String get smsPassword => _getEnvVariable('SMS_PASSWORD');
  static String get smsCacheKey => _getEnvVariable('SMS_CACHE_KEY');
  static String get smsSenderId => _getEnvVariable('SMS_SENDER_ID');

  /// App Store & Play Store
  static String get appStoreId => _getEnvVariable('APP_STORE_ID');
  static String get packageName => _getEnvVariable('PACKAGE_NAME');
}
