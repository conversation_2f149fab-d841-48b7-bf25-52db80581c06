import 'package:hodan_hospital/core/config/di/dependency_injection.dart';
// import 'package:hodan_hospital/features/appointment/data/datasources/local/appointment_database_manager.dart';
// import 'package:hodan_hospital/features/appointment/data/datasources/local/appointment_local_data_source.dart';
import 'package:hodan_hospital/features/appointment/data/datasources/remote/appointment_remote_data_source.dart';
import 'package:hodan_hospital/features/appointment/data/repositories/appointment_repository_impl.dart';
import 'package:hodan_hospital/features/appointment/domain/repositories/appointment_repository.dart';
import 'package:hodan_hospital/features/appointment/domain/usecases/dowload_appointment_pdf_use_case.dart';
import 'package:hodan_hospital/features/appointment/domain/usecases/get_appointment_pdf_use_case.dart';
import 'package:hodan_hospital/features/appointment/domain/usecases/get_appointments_use_case.dart';
import 'package:hodan_hospital/features/appointment/domain/usecases/make_appointment_use_case.dart';
import 'package:hodan_hospital/features/appointment/domain/usecases/proccess_appointment_use_case.dart';
import 'package:hodan_hospital/features/appointment/presentation/bloc/appointment_bloc.dart';
import 'package:injectable/injectable.dart';

@module
abstract class AppointmentModule {
  /// Register Data Sources (Transient)
  @injectable
  AppointmentRemoteDataSource get appointmentRemoteDataSource =>
      AppointmentRemoteDataSourceImpl(
        dioApiClient: sl(),
        httpErrorHandler: sl(),
        paymentService: sl(),
      );

  // @injectable
  // AppointmentLocalDataSource get appointmentLocalDataSource =>
  //     AppointmentLocalDataSourceImpl(
  //       appointmentDatabaseManager: sl(),
  //       flutterSecureStorageServices: sl(),
  //     );

  /// Register Database Manager (Singleton)
  // @lazySingleton
  // AppointmentDatabaseManager get appointmentDatabaseManager =>
  //     AppointmentDatabaseManagerImpl(
  //       databaseErrorHandler: sl(),
  //     );

  /// Register Repository (Singleton)
  @lazySingleton
  AppointmentRepository get appointmentRepository => AppointmentRepositoryImpl(
        // appointmentLocalDataSource: sl(),
        appointmentRemoteDataSource: sl(),
      );

  /// Register Use Cases (Singleton)
  @lazySingleton
  GetAppointmentsUseCase get getAppointmentsUseCase =>
      GetAppointmentsUseCase(repository: sl());
  @lazySingleton
  MakeAppointmentUseCase get makeAppointmentsUseCase =>
      MakeAppointmentUseCase(repository: sl());
  @lazySingleton
  ProcessAppointmentUseCase get processAppointmentUseCase =>
      ProcessAppointmentUseCase(repository: sl());
  @lazySingleton
  DowloadAppointmentPDFUseCase get dowloadAppointmentPDFUseCase =>
      DowloadAppointmentPDFUseCase(appointmentRepository: sl());
  @lazySingleton
  GetAppointmentPDFUseCase get getAppointmentPDFUseCase =>
      GetAppointmentPDFUseCase(appointmentRepository: sl());

  /// Register Bloc (Singleton)
  @lazySingleton
  AppointmentBloc get appointmentBloc => AppointmentBloc(
        getAppointmentsUseCase: sl(),
        makeAppointmentUseCase: sl(),
        processAppointmentUseCase: sl(),
        dowloadAppointmentPDFUseCase: sl(),
        getAppointmentPDFUseCase: sl(),
      );
}
