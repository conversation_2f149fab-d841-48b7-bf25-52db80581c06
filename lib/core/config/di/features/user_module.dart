import 'package:hodan_hospital/features/user/data/datasources/local/user_database_manager.dart';
import 'package:hodan_hospital/features/user/data/datasources/local/user_local_data_source.dart';
import 'package:hodan_hospital/features/user/data/datasources/remote/user_remote_data_source.dart';
import 'package:hodan_hospital/features/user/data/repositories/user_repository_impl.dart';
import 'package:hodan_hospital/features/user/domain/repositories/user_reposiory.dart';
import 'package:hodan_hospital/features/user/domain/usecases/get_current_user_use_case.dart';
import 'package:hodan_hospital/features/user/domain/usecases/get_districts_use_case.dart';
import 'package:hodan_hospital/features/user/domain/usecases/get_users_by_phone_number_use_case.dart';
import 'package:hodan_hospital/features/user/domain/usecases/register_new_patient_use_case.dart';
import 'package:hodan_hospital/features/user/presentation/bloc/user_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:hodan_hospital/core/errors/database_error_handler.dart';
import 'package:hodan_hospital/core/errors/http_error_handler.dart';
import 'package:hodan_hospital/core/network/api_client/dio_api_client.dart';
import 'package:hodan_hospital/core/services/flutter_secure_storage_services.dart';

import '../../../../features/user/domain/usecases/send_feedback_usecase.dart';
import '../../../services/notification_services.dart';

@module
abstract class UserModule {
  /// ✅ Remote Data Source
  @injectable
  UserRemoteDataSource provideUserRemoteDataSource(
    DioApiClient dioApiClient,
    HttpErrorHandler httpErrorHandler,
    FlutterSecureStorageServices flutterSecureStorageServices,
  ) {
    return UserRemoteDataSourceImpl(
      dioApiClient: dioApiClient,
      httpErrorHandler: httpErrorHandler,
      flutterSecureStorageServices: flutterSecureStorageServices,
    );
  }

  /// ✅ Local Data Source
  @injectable
  UserLocalDataSource provideUserLocalDataSource(
      FlutterSecureStorageServices storageServices,
      UserDatabaseManager userDatabaseManager) {
    return UserLocalDataSourceImpl(
      flutterSecureStorageServices: storageServices,
      userDatabaseManager: userDatabaseManager,
    );
  }

  /// ✅ Database Manager (Singleton)
  @lazySingleton
  UserDatabaseManager provideUserDatabaseManager(
      DatabaseErrorHandler databaseErrorHandler) {
    return UserDatabaseManagerImpl(
      databaseErrorHandler: databaseErrorHandler,
    );
  }

  /// ✅ Repository (Singleton)
  @lazySingleton
  UserRepository provideUserRepository(
    UserRemoteDataSource remoteDataSource,
    UserLocalDataSource localDataSource,
    NotificationService notificationService,
  ) {
    return UserRepositoryImpl(
      userLocalDataSource: localDataSource,
      userRemoteDataSource: remoteDataSource,
      notificationService: notificationService,
    );
  }

  /// ✅ Use Cases (Singleton)

  @lazySingleton
  GetUsersByPhoneNumberUseCase provideGetUsersByPhoneNumberUseCase(
      UserRepository repository) {
    return GetUsersByPhoneNumberUseCase(repository: repository);
  }

  @lazySingleton
  GetCurrentUserUseCase provideGetCurrentUserUseCase(
      UserRepository repository) {
    return GetCurrentUserUseCase(repository: repository);
  }

  @lazySingleton
  RegisterNewPatientUseCase provideRegisterNewPatientUseCase(
      UserRepository repository) {
    return RegisterNewPatientUseCase(repository: repository);
  }

  @lazySingleton
  GetDistrictsUseCase provideGetDistrictsUseCase(UserRepository repository) {
    return GetDistrictsUseCase(userRepository: repository);
  }

  @lazySingleton
  SendFeedbackUseCase provideSendFeedbackUseCase(UserRepository repository) {
    return SendFeedbackUseCase(repository: repository);
  }

  /// ✅ Userentication Bloc (Singleton)
  @lazySingleton
  UserBloc provideUserBloc(
    GetUsersByPhoneNumberUseCase getUsersByPhoneNumberUseCase,
    GetCurrentUserUseCase getCurrentUserUseCase,
    RegisterNewPatientUseCase registerNewPatientUseCase,
    GetDistrictsUseCase getDistrictsUseCase,
    SendFeedbackUseCase sendFeedbackUseCase,
  ) {
    return UserBloc(
      getUsersByPhoneNumberUseCase: getUsersByPhoneNumberUseCase,
      getCurrentUserUseCase: getCurrentUserUseCase,
      registerNewPatientUseCase: registerNewPatientUseCase,
      getDistrictsUseCase: getDistrictsUseCase,
      sendFeedbackUseCase: sendFeedbackUseCase,
    );
  }
}
