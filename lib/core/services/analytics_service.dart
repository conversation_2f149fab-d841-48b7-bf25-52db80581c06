import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:hodan_hospital/core/constants/analytics_events.dart';
import 'package:hodan_hospital/core/services/flutter_secure_storage_services.dart';

import '../config/di/dependency_injection.dart';
import '../constants/local_storage_key_constants.dart';

class AnalyticsService {
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;

  FirebaseAnalyticsObserver get observer =>
      FirebaseAnalyticsObserver(analytics: _analytics);

  /// Initialize analytics
  Future<void> initialize() async {
    final localStorage = sl<FlutterSecureStorageServices>();
    final pID =
        await localStorage.readData(key: LocalStorageKeyConstants.userDataKey);
    await _analytics.setUserId(id: pID);
    await _analytics.setAnalyticsCollectionEnabled(true);
  }

  /// Log a generic custom event
  Future<void> logEvent({
    required String name,
    Map<String, Object>? parameters,
  }) async {
    await _analytics.logEvent(
      name: name,
      parameters: parameters != null ? _sanitizeParameters(parameters) : null,
    );
  }

  /// Log screen views manually (if not using observer)
  Future<void> logScreenView({
    required String screenName,
    String? screenClassOverride,
  }) async {
    await _analytics.logScreenView(
      screenName: screenName,
      screenClass: screenClassOverride ?? screenName,
    );
  }

  /// Predefined examples
  Future<void> logNotificationOpened(String source) async {
    await logEvent(name: AnalyticsEvents.notification_opened, parameters: {
      'source': source,
    });
  }

  Future<void> logLogin({
    required String method,
    Map<String, Object>? parameters,
  }) async {
    await _analytics.logLogin(
      loginMethod: method,
      parameters: parameters != null ? _sanitizeParameters(parameters) : null,
    );
  }

  Future<void> logRegister({
    required String method,
    Map<String, Object>? parameters,
  }) async {
    await _analytics.logSignUp(
      signUpMethod: method,
      parameters: parameters != null ? _sanitizeParameters(parameters) : null,
    );
  }

  Future<void> logPaymentInfo({
    String? coupon,
    String? currency,
    String? paymentType,
    double? value,
    List<AnalyticsEventItem>? items,
    Map<String, Object>? parameters,
  }) async {
    await _analytics.logAddPaymentInfo(
      coupon: coupon,
      currency: currency,
      paymentType: paymentType,
      value: value,
      items: items,
      parameters: parameters != null ? _sanitizeParameters(parameters) : null,
    );
  }

  // Error tracking
  Future<void> logError({
    required dynamic error,
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? additionalInfo,
  }) async {
    if (kDebugMode) return;

    await _analytics.logEvent(
      name: AnalyticsEvents.error_occurred,
      parameters: _sanitizeParameters({
        'error': error.toString(),
        'stack_trace': stackTrace?.toString() ?? '',
        'context': context ?? '',
        'timestamp': DateTime.now().toIso8601String(),
        ...additionalInfo != null ? _sanitizeParameters(additionalInfo.cast<String, Object>()) : {},
      }),
    );
  }

  /// Sanitize parameters to ensure all values are strings or numbers for Firebase Analytics
  /// This prevents crashes when boolean or other unsupported types are passed
  Map<String, Object> _sanitizeParameters(Map<String, Object> parameters) {
    final sanitized = <String, Object>{};

    for (final entry in parameters.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value is String || value is num) {
        sanitized[key] = value;
      } else if (value is bool) {
        sanitized[key] = value.toString(); // Convert boolean to string
      } else {
        sanitized[key] = value.toString(); // Convert other types to string
      }
    }

    return sanitized;
  }
}
