import 'dart:convert';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:hodan_hospital/core/services/analytics_service.dart';
import '../config/di/dependency_injection.dart';
import '../config/logger/app_logger.dart';

class NotificationService {
  static final AnalyticsService _analyticsService = sl<AnalyticsService>();

  // TripRequestReceived? _pendingTripRequest;

  // TripRequestReceived? get pendingTripRequest => _pendingTripRequest;

  final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  Future<void> initialize() async {
    // Configure local notifications
    const androidSettings = AndroidInitializationSettings(
      // '@mipmap/ic_launcher',
      '@mipmap/launcher_icon',
    );
    const iosSettings = DarwinInitializationSettings();

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await FirebaseMessaging.instance
        .setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: (response) {
        final payload = response.payload;
        AppLogger().info('🔔 Local notification tapped. Payload: $payload');

        ////** WORKS CORRECT AND IS STORING PENDING TRIP REQUEST BUT FOR MOW NOT NEEDED  */
        //   if (payload != null) {
        //     try {
        //       final data = json.decode(payload);
        //       AppLogger().info("✅ Decoded notification payload: $data");

        //       _handleNotificationTap(
        //         RemoteMessage(data: Map<String, dynamic>.from(data)),
        //       );
        //     } catch (e, s) {
        //       AppLogger().error('❌ Failed to parse notification payload: $e',
        //           stackTrace: s);
        //     }
        //   } else {
        //     AppLogger().warning("⚠️ Notification tapped but payload was null.");
        //   }
      },
    );

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle when Patient taps on notification
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

    // ✅ Handle cold start notification taps
    final initialMessage = await FirebaseMessaging.instance.getInitialMessage();
    if (initialMessage != null) {
      AppLogger().info(
        '📲 App launched from terminated state via notification',
      );

      await _logNotificationEvent(
        eventType: 'notification_tap_cold_start',
        message: initialMessage,
      );

      _handleNotificationTap(initialMessage);
    }

    //* IPORTANT BUT WILL IMPLEMENT LATER
    // final prefs = await SharedPreferences.getInstance();
    // final storedPayload = prefs.getString('pendingTripPayload');
    // if (storedPayload != null) {
    //   AppLogger().info("📦 Restoring trip request from stored payload.");
    //   final data = json.decode(storedPayload);
    //   final event =
    //       _parseNewTripRequestPayload(Map<String, dynamic>.from(data));
    //   if (event != null) {
    //     _pendingTripRequest = event;
    //     AppLogger().info('✅ Restored pending trip from stored payload.');
    //     await prefs.remove('pendingTripPayload');
    //   }
    // }
  }

  Future<void> requestPermissions() async {
    try {
      final settings = await _messaging.requestPermission();

      AppLogger().info(
        'Notification permission status: ${settings.authorizationStatus}',
      );
    } catch (e) {
      AppLogger().error('Failed to request notification permissions: $e');
    }
  }

  Future<String?> getToken() async {
    try {
      return await _messaging.getToken();
    } catch (e) {
      AppLogger().error('Failed to get FCM token: $e');
      return null;
    }
  }

  void _handleForegroundMessage(RemoteMessage message) async {
    AppLogger().info(
      'Received foreground message: ${message.notification?.title}',
    );

    if (message.notification != null) {
      // Show local notification
      _showLocalNotification(message);
    }
  }

  void _handleNotificationTap(RemoteMessage message) async {
    AppLogger().info(
      'Patient tapped on notification: ${message.notification?.title}',
    );
    // final data = message.data;

    // final event = _parseNewTripRequestPayload(data);
    // if (event != null) {
    // _pendingTripRequest = event;
    // AppLogger().info(
    // '✅ Stored pending trip request for tripId: ${event.tripId}',
    // );
    // }

    await _logNotificationEvent(eventType: 'tap_foreground', message: message);
  }

  Future<void> _showLocalNotification(RemoteMessage message) async {
    final androidDetails = const AndroidNotificationDetails(
      'ambulance_Patients_channel',
      'Ambulance Patients Notifications',
      channelDescription: 'Notifications from the ambulance Patientss app',
      importance: Importance.high,
      priority: Priority.high,
    );

    final iosDetails = const DarwinNotificationDetails();
    final details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      message.hashCode,
      message.notification?.title,
      message.notification?.body,
      details,
      payload: json.encode(message.data),
    );
  }

  // TripRequestReceived? _parseNewTripRequestPayload(Map<String, dynamic> data) {
  //   try {
  //     final tripId = data['tripId'] ?? 'N/A';
  //     final passengerLat = double.tryParse(data['lat'] ?? '');
  //     final passengerLng = double.tryParse(data['lng'] ?? '');
  //     final expiryStr = data['expiresAt'];
  //     final expiryTime = expiryStr != null
  //         ? DateTime.tryParse(expiryStr)
  //         : DateTime.now().add(const Duration(seconds: 60));

  //     if (passengerLat == null || passengerLng == null || expiryTime == null) {
  //       return null;
  //     }

  //     return TripRequestReceived(
  //       tripId: tripId,
  //       passengerId: data['userId'] ?? 'N/A',
  //       passengerName: data['userName'] ?? 'Passenger',
  //       passengerPhone: data['userPhone'] ?? '',
  //       passengerLocation: LatLng(passengerLat, passengerLng),
  //       expiryTime: expiryTime,
  //     );
  //   } catch (e) {
  //     AppLogger().error('Error parsing new trip request notification: $e');
  //     return null;
  //   }
  // }

  void clearPendingTrip() {
    // _pendingTripRequest = null;
  }

  // Add notification analytics
  Future<void> _logNotificationEvent({
    required String eventType,
    required RemoteMessage message,
  }) async {
    await _analyticsService.logEvent(
      name: 'notification_$eventType',
      parameters: {
        'title': message.notification?.title ?? 'N/A',
        'body': message.notification?.body ?? 'N/A',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }
}

// Top-level function to handle background messages
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Need to ensure Firebase is initialized here too
  await Firebase.initializeApp();
  print('[FCM] Background data payload: ${message.data}');

  ////* You can add some simple logic here, like storing payload in shared preferences\
  // if (payload != null) {
  // final prefs = await SharedPreferences.getInstance();
  // await prefs.setString('pendingTripPayload', payload);
  // }
}
