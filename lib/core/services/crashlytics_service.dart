import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart' show kDebugMode, defaultTargetPlatform;
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../config/di/dependency_injection.dart';
import '../constants/local_storage_key_constants.dart';
import 'flutter_secure_storage_services.dart';

class CrashlyticsService {
  final FirebaseCrashlytics _crashlytics = FirebaseCrashlytics.instance;

  Future<void> initialize() async {
    final localStorage = sl<FlutterSecureStorageServices>();
    final pID =
        await localStorage.readData(key: LocalStorageKeyConstants.userDataKey);

    // await _crashlytics.setCrashlyticsCollectionEnabled(!kDebugMode);
    await _crashlytics.setCrashlyticsCollectionEnabled(true);

    if (pID != null) {
      await Future.wait([
        _crashlytics.setUserIdentifier(pID),
        _crashlytics.setCustomKey('user_pid', pID),
      ]);
    }

    // Set additional context that might be useful for debugging
    await _crashlytics.setCustomKey('app_version', await _getAppVersion());
    await _crashlytics.setCustomKey(
        'platform', defaultTargetPlatform.toString());
  }

  Future<String> _getAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      return '${packageInfo.version}+${packageInfo.buildNumber}';
    } catch (e) {
      return 'Unknown';
    }
  }

  Future<void> logError({
    required dynamic error, // Accept any error type
    StackTrace? stackTrace,
    String? context,
    bool fatal = false,
    Map<String, dynamic>? additionalInfo,
  }) async {
    if (kDebugMode) return;

    // Convert error to string if it isn't already
    final errorString = error is String ? error : error.toString();

    // Record the error
    await _crashlytics.recordError(
      errorString,
      stackTrace,
      reason: context ?? 'Unhandled exception',
      fatal: fatal,
      information: additionalInfo != null
          ? additionalInfo.entries.map((e) => '${e.key}: ${e.value}').toList()
          : const [],
    );

    // Set custom keys for additional context
    if (additionalInfo != null) {
      await Future.wait(
        additionalInfo.entries.map(
          (e) => _crashlytics.setCustomKey(e.key, e.value.toString()),
        ),
      );
    }
  }

  Future<void> recordFlutterFatalError(FlutterErrorDetails details) async {
    // if (kDebugMode) return;

    await _crashlytics.recordFlutterFatalError(details);
  }

  Future<void> logBreadcrumb(
    String message, {
    Map<String, dynamic>? data,
  }) async {
    await _crashlytics.log(message);
    if (data != null) {
      await Future.wait(
        data.entries.map(
          (e) => _crashlytics.setCustomKey(
              'breadcrumb_${e.key}', e.value.toString()),
        ),
      );
    }
  }

  Future<void> recordNonFatalException({
    required dynamic exception,
    StackTrace? stackTrace,
    String? reason,
    Map<String, dynamic>? context,
  }) async {
    if (kDebugMode) return;

    await logError(
      error: exception,
      stackTrace: stackTrace ?? StackTrace.current,
      context: reason ?? 'Non-fatal exception',
      // fatal: false,
      additionalInfo: context,
    );
  }

  Future<void> recordFlutterError(FlutterErrorDetails details) async {
    await logError(
      error: details.exception,
      stackTrace: details.stack,
      context: 'FlutterError: ${details.context}',
      additionalInfo: {
        'library': details.library,
        'silent': details.silent,
      },
    );
  }
}
