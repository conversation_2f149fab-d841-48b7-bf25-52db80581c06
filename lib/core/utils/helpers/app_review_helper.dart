import 'dart:io' show Platform;

import 'package:flutter/material.dart';
import 'package:hodan_hospital/core/config/di/dependency_injection.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/services/flutter_secure_storage_services.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:url_launcher/url_launcher.dart' show LaunchMode;

import '../../config/enviroment/enviroment_config.dart';
import '../../constants/app_constants.dart';
import '../../constants/local_storage_key_constants.dart';
import 'url_laucher_helper.dart';

class AppReviewHelper {
  static final AppReviewHelper _instance = AppReviewHelper._internal();
  factory AppReviewHelper() => _instance;
  AppReviewHelper._internal();

  static final FlutterSecureStorageServices _flutterSecureStorageServices =
      sl<FlutterSecureStorageServices>();

  static final UrlLauncherHelper _urlLauncherHelper = UrlLauncherHelper();

  static final InAppReview _inAppReview = InAppReview.instance;
  static final String _appStoreId = EnvironmentConfig.appStoreId;
  static final String _packageName = EnvironmentConfig.packageName;
  static final String _appName = AppConstants.appName;
  static final bool isIos = Platform.isIOS;

  /// Launch in-app review if available, else fallback to external store review
  Future<void> launchSmartReview(BuildContext context) async {
    // AppLogger().info('Checking if we should show review');
    if (!await _shouldShowReview()) return;
    AppLogger().info('Showing review');

    try {
      if (await _inAppReview.isAvailable()) {
        AppLogger().info('Launching in-app review');
        await _inAppReview.requestReview();
        await _storeReviewData();
      } else {
        if (!context.mounted) return;
        AppLogger()
            .info('In-app review not available, launching $storeTitle review');

        await _showReviewDialog(context); // Let user choose to go to store
      }
    } catch (e, stackTrace) {
      AppLogger().error('Review flow failed', error: e, stackTrace: stackTrace);
      if (!context.mounted) return;
      await launchAppStoreReview(context);
    }
  }

  Future<void> _storeReviewData() async {
    await _flutterSecureStorageServices.storeData(
      key: LocalStorageKeyConstants.reviewed,
      value: 'true',
    );
    await _flutterSecureStorageServices.storeData(
      key: LocalStorageKeyConstants.lastReviewPromptDate,
      value: DateTime.now().toIso8601String(),
    );
  }

  Future<void> _showReviewDialog(BuildContext context) async {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Enjoying $_appName?'),
        content: Text('Would you like to rate us in the $storeTitle?'),
        actions: [
          TextButton(
            onPressed: () => context.popRoute(),
            child: const Text('Not Now'),
          ),
          TextButton(
            onPressed: () async {
              context.popRoute();
              await launchAppStoreReview(context);
            },
            child: const Text('Rate Now'),
          ),
        ],
      ),
    );
  }

  /// Launch AppStore or PlayStore for app review
  Future<void> launchAppStoreReview(BuildContext context) async {
    final appStoreUrl =
        'https://apps.apple.com/app/id$_appStoreId?action=write-review';
    final playStoreUrl =
        'https://play.google.com/store/apps/details?id=$_packageName&showAllReviews=true';

    final url = isIos ? appStoreUrl : playStoreUrl;

    final fallbackUrl = isIos
        ? 'https://apps.apple.com/app/id$_appStoreId'
        : 'https://play.google.com/store/apps/details?id=$_packageName';

    AppLogger().info('Launching $storeTitle review: $url');

    await _urlLauncherHelper.launchUrl(
      context: context,
      url: url,
      fallbackUrl: fallbackUrl,
      mode: LaunchMode.externalApplication,
      onSuccess: (url) async {
        AppLogger().info('Launched $storeTitle review: $url');
      },
      onError: (e, stackTrace) async {
        AppLogger().error(
          'Error launching $storeTitle review: $e',
          stackTrace: stackTrace,
        );
      },
    );
  }

  Future<bool> _shouldShowReview({int daysBetweenPrompts = 30}) async {
    final reviewed = await _hasReviewed;
    AppLogger().info('Has reviewed: $reviewed');
    // if (reviewed) return false;

    final lastPrompted = await _flutterSecureStorageServices.readData(
      key: LocalStorageKeyConstants.lastReviewPromptDate,
    );
    AppLogger().info('Last prompted: $lastPrompted');

    // Don't show more than once per month
    // if (lastPrompted != null) {
    // try {
    //   final lastDate = DateTime.parse(lastPrompted);
    //   final daysSince = DateTime.now().difference(lastDate).inDays;
    //   AppLogger().info('Days since last prompted: $daysSince');
    //   return daysSince > daysBetweenPrompts && daysSince >= 0;
    // } catch (_) {
    //   return true; // fallback to showing review if corrupted
    // }
    // }

    return true;
  }

  Future<bool> get _hasReviewed async {
    final reviewed = await _flutterSecureStorageServices.readData(
      key: LocalStorageKeyConstants.reviewed,
    );
    return reviewed == 'true';
  }

  String get storeTitle => isIos ? 'App Store' : 'Play Store';
}
