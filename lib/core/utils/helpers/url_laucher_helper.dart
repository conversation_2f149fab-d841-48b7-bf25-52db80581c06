import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart' as url_launcher;

import 'snack_bar_helper.dart';

class UrlLauncherHelper {
  static final UrlLauncherHelper _instance = UrlLauncherHelper._internal();
  factory UrlLauncherHelper() => _instance;
  UrlLauncherHelper._internal();

  /// Launch a phone call
  Future<void> launchTel({
    required BuildContext context,
    required String phoneNumber,
  }) async {
    final formatted = _formatPhoneNumber(phoneNumber);
    final url = 'tel:$formatted';
    await launchUrl(context: context, url: url);
  }

  /// Launch an email
  Future<void> launchEmail({
    required BuildContext context,
    required String toEmail,
    String? subject,
    String? body,
  }) async {
    final url = Uri(
      scheme: 'mailto',
      path: toEmail,
      query: _encodeQueryParams({
        if (subject != null) 'subject': subject,
        if (body != null) 'body': body,
      }),
    ).toString();
    await launchUrl(context: context, url: url);
  }

  /// Launch WhatsApp chat
  Future<void> launchWhatsApp({
    required BuildContext context,
    required String phoneNumber,
    String message = '',
  }) async {
    final formatted = _formatPhoneNumber(phoneNumber);
    final url = 'https://wa.me/$formatted?text=${Uri.encodeComponent(message)}';
    await launchUrl(context: context, url: url);
  }

  /// Format Somali phone numbers to E.164 style: +252XXXXXXXXX
  String _formatPhoneNumber(String input) {
    // Remove all non-digit characters, but keep +
    final String cleaned = input.replaceAll(RegExp(r'[^\d+]'), '');

    if (cleaned.startsWith('+252')) {
      return cleaned; // Already in correct format
    } else if (cleaned.startsWith('252')) {
      return '+$cleaned';
    } else if (cleaned.startsWith('0')) {
      // Replace leading 0 with +252
      return '+252${cleaned.substring(1)}';
    } else if (cleaned.startsWith('+')) {
      // Assume user added another country code, which is not Somalia
      // You could throw or sanitize here depending on your app logic
      return cleaned;
    } else {
      // Assume it's local number without prefix
      return '+252$cleaned';
    }
  }

  /// Encode query parameters for mailto links
  String _encodeQueryParams(Map<String, String> params) {
    return params.entries
        .map(
          (e) =>
              '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}',
        )
        .join('&');
  }

  /// Launch any URL and handle errors
  Future<void> launchUrl({
    required BuildContext context,
    required String url,
    url_launcher.LaunchMode mode = url_launcher.LaunchMode.platformDefault,
    String? fallbackUrl,
    Future<void> Function(String url)? onSuccess,
    Future<void> Function(Object e, StackTrace stackTrace)? onError,
  }) async {
    try {
      final uri = Uri.parse(url);

      if (await url_launcher.canLaunchUrl(uri)) {
        final launched = await url_launcher.launchUrl(uri, mode: mode);

        if (launched) {
          onSuccess?.call(url);
          return;
        }
      }

      // Try fallback if main URL fails
      if (fallbackUrl != null) {
        final fallbackUri = Uri.parse(fallbackUrl);
        if (await url_launcher.canLaunchUrl(fallbackUri)) {
          final launched =
              await url_launcher.launchUrl(fallbackUri, mode: mode);
          if (launched) return;
        }
      }

      if (!context.mounted) return;
      _showError(context, 'Could not launch URL');
    } catch (e, stackTrace) {
      onError?.call(e, stackTrace);
      if (!context.mounted) return;
      _showError(context, 'Failed to open: ${e.toString()}');
    }
  }

  void _showError(BuildContext context, String message) {
    SnackBarHelper.showErrorSnackBar(context, message: message);
  }
}
