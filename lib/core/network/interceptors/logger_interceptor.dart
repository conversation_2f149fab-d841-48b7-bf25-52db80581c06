import 'package:dio/dio.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';

import '../../config/di/dependency_injection.dart';
import '../../services/analytics_service.dart';
import '../../services/crashlytics_service.dart';

class LoggerInterceptor extends Interceptor {
  final _crashlytics = sl<CrashlyticsService>();
  final _analytics = sl<AnalyticsService>();

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    AppLogger().info('🔗 Request: ${options.uri}');
    AppLogger().info('🔗 Request Data: ${options.data}');
    // AppLogger().info('🔗 Request Headers: ${options.headers}');
    // AppLogger().info("🔗 Request Method: ${options.method}");
    // AppLogger().info("🔗 Request Path: ${options.path}");

    return handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    AppLogger().info('✅ Response Status Code: ${response.statusCode}');
    AppLogger().info('✅ Response Data: ${response.data}');
    // AppLogger().info('✅ Response Headers: ${response.headers}');
    // AppLogger()
    // .info('✅ Response Request Path: ${response.requestOptions.path}');
    return handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final errorDetails = {
      'url': err.requestOptions.uri.toString(),
      'method': err.requestOptions.method,
      'status_code': err.response?.statusCode,
      'request_data': err.requestOptions.data.toString(),
      'response_data': err.response?.data.toString(),
      'headers': err.requestOptions.headers.toString(),
    };

    // Log to console
    AppLogger().error('❌ Dio Error: ${err.message}',
        error: errorDetails, stackTrace: err.stackTrace);

    // Log to Firebase Crashlytics
    await _crashlytics.logError(
      error: err,
      stackTrace: err.stackTrace,
      context: 'Dio Error',
      additionalInfo: errorDetails,
    );

    // Optionally log to Firebase Analytics (non-fatal)
    await _analytics.logError(
      error: err,
      stackTrace: err.stackTrace,
      context: 'Dio Request Failure',
      additionalInfo: errorDetails,
    );

    return handler.next(err);
  }
}
