// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCqequmYsFF6MPpKdnU3tP1Kdb8IFci4QA',
    appId: '1:59552903675:web:4d0d567ae0985fe6f25b5e',
    messagingSenderId: '59552903675',
    projectId: 'hodan-medical-app',
    authDomain: 'hodan-medical-app.firebaseapp.com',
    storageBucket: 'hodan-medical-app.firebasestorage.app',
    measurementId: 'G-CX6XN4REZJ',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBF9IXVCKadwABihIh2vGndr0bylDGJJSA',
    appId: '1:59552903675:android:c8b5fb9abf349224f25b5e',
    messagingSenderId: '59552903675',
    projectId: 'hodan-medical-app',
    storageBucket: 'hodan-medical-app.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCp_ZVXdN-KqQLoGB58BmyMugA_N5u-R2Q',
    appId: '1:59552903675:ios:6790f1cb94d29a3cf25b5e',
    messagingSenderId: '59552903675',
    projectId: 'hodan-medical-app',
    storageBucket: 'hodan-medical-app.firebasestorage.app',
    iosBundleId: 'com.rasiin.hodanHospital',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCp_ZVXdN-KqQLoGB58BmyMugA_N5u-R2Q',
    appId: '1:59552903675:ios:e9b89d1d0a9b2c85f25b5e',
    messagingSenderId: '59552903675',
    projectId: 'hodan-medical-app',
    storageBucket: 'hodan-medical-app.firebasestorage.app',
    iosBundleId: 'com.example.hodanHospital',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCqequmYsFF6MPpKdnU3tP1Kdb8IFci4QA',
    appId: '1:59552903675:web:c311e66d229c2a9ff25b5e',
    messagingSenderId: '59552903675',
    projectId: 'hodan-medical-app',
    authDomain: 'hodan-medical-app.firebaseapp.com',
    storageBucket: 'hodan-medical-app.firebasestorage.app',
    measurementId: 'G-4HF00QQCM9',
  );
}
