import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/core/utils/helpers/snack_bar_helper.dart';
import 'package:hodan_hospital/features/main/presentation/pages/main_page.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_app_bar.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_button.dart';
import 'package:pinput/pinput.dart';
import 'package:hodan_hospital/features/authentication/presentation/bloc/authentication_bloc.dart';

import '../../../../core/config/di/dependency_injection.dart';
import '../../../../core/constants/analytics_events.dart';
import '../../../../core/enums/button_state.dart';
import '../../../../core/errors/app_failure.dart';
import '../../../../core/services/analytics_service.dart';
import '../../data/models/register_form_data.dart';

class OtpVerificationPage extends StatefulWidget {
  final String phoneNumber;
  final RegisterFormData? registerFormData;

  const OtpVerificationPage({
    super.key,
    required this.phoneNumber,
    this.registerFormData,
  });

  @override
  State<OtpVerificationPage> createState() => _OtpVerificationPageState();
}

class _OtpVerificationPageState extends State<OtpVerificationPage> {
  late bool isLoginFlow;
  final TextEditingController _otpController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  static const int _initialCountdown = 120;
  int _resendCountdown = _initialCountdown;

  @override
  void initState() {
    super.initState();
    isLoginFlow = widget.registerFormData == null;
    _startResendCountdown();
  }

  void _startResendCountdown() {
    Future.delayed(const Duration(seconds: 1), () {
      if (!mounted) return;
      if (_resendCountdown > 0) {
        setState(() => _resendCountdown--);
        _startResendCountdown();
      }
    });
  }

  String _formatTime(int seconds) {
    final minutes = (seconds ~/ 60).toString().padLeft(2, '0');
    final secs = (seconds % 60).toString().padLeft(2, '0');
    return '$minutes:$secs';
  }

  void _onVerify(String otp) {
    if (otp.length == 6) {
      final analyticsService = context.analyticsService;

      // Track OTP verification initiated
      analyticsService.logEvent(
        name: AnalyticsEvents.otp_verification_initiated,
        parameters: {
          'phone_number': widget.phoneNumber,
          'flow_type': isLoginFlow ? 'login' : 'registration',
          'otp_length': otp.length.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      context.read<AuthenticationBloc>().add(
            VerifyOtpEvent(
              mobileNumber: widget.phoneNumber,
              otp: otp,
            ),
          );
    }
  }

  void _onResend() {
    if (_resendCountdown > 0) return;

    final analyticsService = context.analyticsService;

    // Track OTP resend requested
    analyticsService.logEvent(
      name: AnalyticsEvents.otp_resend_requested,
      parameters: {
        'phone_number': widget.phoneNumber,
        'flow_type': isLoginFlow ? 'login' : 'registration',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );

    context.read<AuthenticationBloc>().add(
          ResendOtpEvent(
            mobileNumber: widget.phoneNumber,
            isLogin: isLoginFlow,
          ),
        );
    setState(() => _resendCountdown = _initialCountdown);
    _startResendCountdown();
  }

  void _registerPatient() {
    final formData = widget.registerFormData;
    final analyticsService = context.analyticsService;

    context.authenticationBloc.add(RegisterEvent(
      fullName: formData?.fullName ?? '',
      gender: formData?.gender ?? '',
      age: formData?.age ?? 1,
      // ageType: formData?.ageType ?? 'Year',
      mobileNumber: formData?.mobileNumber ?? '',
      district: formData?.district ?? '',
    ));

    // Track registration initiated
    analyticsService.logEvent(
      name: AnalyticsEvents.registration_initiated,
      parameters: {
        'phone_number': widget.phoneNumber,
        'timestamp': DateTime.now().toIso8601String(),
        'flow_type': 'registration',
      },
    );
  }

  void _handleFailure(AppFailure failure) {
    SnackBarHelper.showErrorSnackBar(context,
        message: failure.getErrorMessage());
  }

  @override
  Widget build(BuildContext context) {
    final pinTheme = PinTheme(
      width: 56,
      height: 56,
      textStyle: const TextStyle(fontSize: 22, fontWeight: FontWeight.w600),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
    );

    return Scaffold(
      appBar: const AnimatedAppBar(title: 'Verify OTP'),
      body: BlocListener<AuthenticationBloc, AuthenticationState>(
        listener: _handleAuthState,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Verify your number',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 8),
              Text(
                'Enter the 6-digit code sent to\n${widget.phoneNumber}',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.grey.shade600,
                    ),
              ),
              const SizedBox(height: 32),
              Form(
                key: _formKey,
                child: Center(
                  child: Pinput(
                    controller: _otpController,
                    length: 6,
                    validator: (value) => (value == null || value.length != 6)
                        ? 'Enter a 6-digit code'
                        : null,
                    onCompleted: _onVerify,
                    defaultPinTheme: pinTheme,
                    focusedPinTheme: pinTheme.copyWith(
                      decoration: pinTheme.decoration!.copyWith(
                        border: Border.all(
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 35),
              BlocBuilder<AuthenticationBloc, AuthenticationState>(
                buildWhen: (prev, curr) =>
                    curr is AuthenticationVerifyOtpLoading ||
                    curr is VerifyOtpFailure ||
                    curr is VerifyOtpSuccess,
                builder: (context, state) {
                  final isLoading = state is AuthenticationVerifyOtpLoading;
                  return CustomButton(
                    width: double.infinity,
                    buttonText: 'Verify',
                    buttonState:
                        isLoading ? ButtonState.loading : ButtonState.normal,
                    onTap: () {
                      if (_formKey.currentState?.validate() ?? false) {
                        _onVerify(_otpController.text);
                      }
                    },
                  );
                },
              ),
              const SizedBox(height: 24),
              Center(
                child: BlocBuilder<AuthenticationBloc, AuthenticationState>(
                  buildWhen: (prev, curr) =>
                      curr is AuthenticationResendOtpLoading ||
                      curr is ResendOtpFailure ||
                      curr is ResendOtpSuccess,
                  builder: (context, state) {
                    final isResending = state is AuthenticationResendOtpLoading;
                    return isResending
                        ? const CircularProgressIndicator()
                        : TextButton(
                            onPressed: _resendCountdown == 0 ? _onResend : null,
                            child: Text(
                              _resendCountdown == 0
                                  ? 'Resend Code'
                                  : 'Resend in ${_formatTime(_resendCountdown)}',
                              style: TextStyle(
                                color: _resendCountdown == 0
                                    ? Theme.of(context).colorScheme.primary
                                    : Colors.grey,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleAuthState(BuildContext context, AuthenticationState state) {
    AppLogger().info('OTP Page State: $state');
    final analyticsService = context.analyticsService;

    if (state is VerifyOtpSuccess) {
      // Track OTP verification success
      analyticsService.logEvent(
        name: AnalyticsEvents.otp_verified,
        parameters: {
          'phone_number': widget.phoneNumber,
          'flow_type': isLoginFlow ? 'login' : 'registration',
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      if (isLoginFlow) {
        // Track successful login completion
        analyticsService.logEvent(
          name: AnalyticsEvents.login_success,
          parameters: {
            'phone_number': widget.phoneNumber,
            'method': 'phone_number',
            'timestamp': DateTime.now().toIso8601String(),
            'flow_type': 'login',
            'authentication_complete': 'true',
          },
        );

        SnackBarHelper.showSuccessSnackBar(context,
            message: 'Otp Verified Successfully');
        context.pushAndRemoveUntilRoute(const MainPage());
      } else {

        _registerPatient();
      }
    } else if (state is VerifyOtpFailure) {
      // Track OTP verification failure
      analyticsService.logEvent(
        name: AnalyticsEvents.otp_verification_failed,
        parameters: {
          'phone_number': widget.phoneNumber,
          'flow_type': isLoginFlow ? 'login' : 'registration',
          'error_message': state.appFailure.getErrorMessage(),
          'error_type': state.appFailure.runtimeType.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      _handleFailure(state.appFailure);
    } else if (state is ResendOtpSuccess) {
      // Track OTP resend success
      analyticsService.logEvent(
        name: AnalyticsEvents.otp_resend_success,
        parameters: {
          'phone_number': widget.phoneNumber,
          'flow_type': isLoginFlow ? 'login' : 'registration',
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      SnackBarHelper.showSuccessSnackBar(context,
          message: 'Otp Resent Successfully');
    } else if (state is ResendOtpFailure) {
      // Track OTP resend failure
      analyticsService.logEvent(
        name: AnalyticsEvents.otp_resend_failed,
        parameters: {
          'phone_number': widget.phoneNumber,
          'flow_type': isLoginFlow ? 'login' : 'registration',
          'error_message': state.appFailure.getErrorMessage(),
          'error_type': state.appFailure.runtimeType.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      _handleFailure(state.appFailure);
    } else if (state is RegistrationFailure) {
      // Track registration failure
      analyticsService.logEvent(
        name: AnalyticsEvents.registration_failed,
        parameters: {
          'phone_number': widget.phoneNumber,
          'error_message': state.appFailure.getErrorMessage(),
          'error_type': state.appFailure.runtimeType.toString(),
          'timestamp': DateTime.now().toIso8601String(),
          'flow_type': 'registration',
          'stage': 'final_registration',
        },
      );

      _handleFailure(state.appFailure);
    } else if (state is RegistrationSuccess) {
      // Track registration success
      analyticsService.logEvent(
        name: AnalyticsEvents.registration_success,
        parameters: {
          'phone_number': widget.phoneNumber,
          'method': 'phone_number',
          'timestamp': DateTime.now().toIso8601String(),
          'flow_type': 'registration',
          'registration_complete': 'true',
        },
      );

      SnackBarHelper.showSuccessSnackBar(context,
          message: 'Registration Successful');
      context.pushAndRemoveUntilRoute(const MainPage());
    }
  }
}
