import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/button_state.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/core/utils/helpers/form_validation_helper.dart';
import 'package:hodan_hospital/core/utils/helpers/snack_bar_helper.dart';
import 'package:hodan_hospital/features/authentication/data/models/register_form_data.dart';
import 'package:hodan_hospital/features/authentication/presentation/bloc/authentication_bloc.dart';
import 'package:hodan_hospital/features/authentication/presentation/pages/login_page.dart';
import 'package:hodan_hospital/features/authentication/presentation/pages/otp_verification_page.dart';
import 'package:hodan_hospital/features/shared/data/models/gender.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_item_wrapper.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_button.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_drop_down.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_textfield.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/signup_or_login_promt.dart';
import 'package:hodan_hospital/features/user/domain/entities/district_entity.dart';
import 'package:hodan_hospital/features/user/presentation/bloc/user_bloc.dart';

import '../../../../core/config/di/dependency_injection.dart';
import '../../../../core/constants/analytics_events.dart';
import '../../../../core/errors/app_failure.dart';
import '../../../../core/services/analytics_service.dart';
import '../../../../core/utils/helpers/somali_phone_number_formatter.dart';

class RegisterForm extends StatefulWidget {
  const RegisterForm({super.key});

  @override
  State<RegisterForm> createState() => _RegisterFormState();
}

class _RegisterFormState extends State<RegisterForm> {
  final registerFormKey = GlobalKey<FormState>();
  final nameController = TextEditingController();
  final ageController = TextEditingController();
  final phoneController = TextEditingController();

  Gender? selectedGender;
  DistrictEntity? selectedDistrict;

  @override
  void dispose() {
    nameController.dispose();
    ageController.dispose();
    phoneController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();

    // Track registration form initiated
    final analyticsService = context.analyticsService;
    analyticsService.logEvent(
      name: AnalyticsEvents.registration_initiated,
      parameters: {
        'timestamp': DateTime.now().toIso8601String(),
        'flow_type': 'registration',
      },
    );

    // Load districts
    final districts = context.userBloc.districts;
    if (districts.isEmpty) {
      context.userBloc.add(const GetDistrictsEvent(forceFetch: true));
    }
  }

  @override
  Widget build(BuildContext context) {
    final authBloc = context.authenticationBloc;

    return BlocListener<AuthenticationBloc, AuthenticationState>(
      listener: _authListener,
      child: Form(
        key: registerFormKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              /// some space on top
              SizedBox(height: 50.h),
              _buildHeader(),
              SizedBox(height: 20.h),
              _buildNameField(),
              SizedBox(height: 20.h),
              _buildGenderDropdown(),
              SizedBox(height: 20.h),
              _buildAgeField(),
              SizedBox(height: 20.h),
              _buildPhoneNumberField(),
              SizedBox(height: 20.h),
              _buildDistrictDropdown(),
              SizedBox(height: 30.h),
              BlocBuilder<AuthenticationBloc, AuthenticationState>(
                builder: (context, state) {
                  return _buildSignUpButton(authBloc, state);
                },
              ),
              SizedBox(height: 20.h),
              BlocBuilder<AuthenticationBloc, AuthenticationState>(
                builder: (context, state) {
                  return _buildLoginPrompt(
                      state is AuthenticationRegisterLoading);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return AnimatedItemWrapper(
      child: Column(
        children: [
          SizedBox(height: 40.h),
          Text('Registration', style: context.textTheme.headlineMedium),
          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  Widget _buildNameField() {
    return AnimatedItemWrapper(
      delay: const Duration(milliseconds: 500),
      child: CustomTextField(
        controller: nameController,
        labelText: 'Full Name',
        validator: (value) => FormValidationHelper.validateName(value: value),
      ),
    );
  }

  Widget _buildGenderDropdown() {
    return AnimatedItemWrapper(
      delay: const Duration(milliseconds: 600),
      child: CustomDropDown(
        labelText: 'Gender',
        value: selectedGender,
        displayItem: (Gender? value) => value?.genderType,
        items: Gender.values,
        onChanged: (Gender? value) => setState(() => selectedGender = value),
        validator: (value) => FormValidationHelper.validateRequiredField(
            value: value?.genderType, fieldName: 'Gender'),
      ),
    );
  }

  Widget _buildAgeField() {
    return AnimatedItemWrapper(
      delay: const Duration(milliseconds: 700),
      child: CustomTextField(
        controller: ageController,
        labelText: 'Age',
        keyboardType: TextInputType.number,
        validator: (value) => FormValidationHelper.validateAge(value: value),
      ),
    );
  }

  // Widget _buildPhoneNumberField() {
  //   return AnimatedItemWrapper(
  //     delay: const Duration(milliseconds: 800),
  //     child: CustomTextField(
  //       controller: phoneController,
  //       labelText: 'Phone Number',
  //       hintText: '061XXXXXXX',
  //       keyboardType: TextInputType.phone,
  //       validator: (value) =>
  //           FormValidationHelper.validatePhoneNumber(value: value),
  //     ),
  //   );
  // }

  Widget _buildPhoneNumberField() {
    return AnimatedItemWrapper(
      delay: const Duration(milliseconds: 200),
      child: CustomTextField(
        controller: phoneController,
        keyboardType: TextInputType.phone,
        textInputAction: TextInputAction.done,
        hintText: '61XXXXXXX',
        inputFormatters: [
          SomaliPhoneNumberFormatter(allowLeadingZero: true),
        ],
        labelStyle: context.textTheme.labelMedium?.copyWith(
          fontSize: 16.sp,
          fontWeight: FontWeight.w600,
        ),
        hintStyle: context.textTheme.bodySmall?.copyWith(
          fontSize: 16.sp,
        ),
        validator: (value) =>
            FormValidationHelper.validatePhoneNumber(value: value),
        prefixIcon: Padding(
          padding: EdgeInsets.symmetric(horizontal: 10.w),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '+252',
                style: context.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: context.appColors.primaryColor,
                ),
              ),
              VerticalDivider(
                width: 1,
                thickness: 1,
                color: context.appColors.primaryColor.withValues(alpha: 0.5),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDistrictDropdown() {
    return AnimatedItemWrapper(
      delay: const Duration(milliseconds: 900),
      child: BlocBuilder<UserBloc, UserState>(
        builder: (context, state) {
          final districts = context.userBloc.districts;
          // if (state is DistrictsLoaded) {
          return CustomDropDown<DistrictEntity>(
            items: districts,
            labelText: 'Territory',
            value: selectedDistrict,
            displayItem: (DistrictEntity? value) => value?.name,
            onChanged: (DistrictEntity? value) =>
                setState(() => selectedDistrict = value),
            validator: (value) => FormValidationHelper.validateRequiredField(
              value: value?.name,
              fieldName: 'Territory',
            ),
          );
          // }
          // return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildSignUpButton(
      AuthenticationBloc authBloc, AuthenticationState state) {
    return AnimatedItemWrapper(
      delay: const Duration(milliseconds: 1000),
      child: CustomButton(
        buttonText: 'Register',
        height: 40.h,
        width: context.screenWidth * 0.6,
        textStyle: context.textTheme.titleMedium?.copyWith(
          fontSize: 20.sp,
          color: context.appColors.whiteColor,
          fontWeight: FontWeight.w600,
        ),
        buttonState:
            // state is AuthenticationRegisterLoading
            state is CanRegisterChecking
                ? ButtonState.loading
                : ButtonState.normal,
        onTap: () {
          if (registerFormKey.currentState!.validate()) {
            final analyticsService = context.analyticsService;
            final phoneNumber = phoneController.text.trim();
            final fullName = nameController.text.trim();

            // Track registration form completion
            analyticsService.logEvent(
              name: AnalyticsEvents.registration_form_completed,
              parameters: {
                'phone_number': phoneNumber,
                'full_name': fullName,
                'age': ageController.text.trim(),
                'gender': selectedGender?.genderType ?? '',
                'district': selectedDistrict?.name ?? '',
                'timestamp': DateTime.now().toIso8601String(),
                'flow_type': 'registration',
              },
            );

            // Also log using standard register method
            analyticsService.logRegister(
              method: 'phone_number',
              parameters: {
                'phone_number': phoneNumber,
                'full_name': fullName,
                'timestamp': DateTime.now().toIso8601String(),
              },
            );

            authBloc.add(CanRegisterEvent(
              mobileNumber: phoneNumber,
              fullName: fullName,
            ));
          }
        },
      ),
    );
  }

  Widget _buildLoginPrompt(bool isDisabled) {
    return AnimatedItemWrapper(
      delay: const Duration(milliseconds: 1100),
      child: SignUpOrLoginPromt(
        promt: 'Login',
        promtType: 'Already have an account?',
        // onTap: () => context.goNamedRoute(RoutePaths.authLogin),
        onTap: () => context.pushAndRemoveUntilRoute(const LoginPage()),
      ),
    );
  }

  void _handleFailure(AppFailure failure) {
    final message = failure.getErrorMessage();
    SnackBarHelper.showErrorSnackBar(context, message: message);
  }

  void _authListener(BuildContext context, AuthenticationState state) {
    final analyticsService = context.analyticsService;
    final phoneNumber = phoneController.text.trim();

    if (state is CanRegisterFailure) {
      // Track registration failure
      analyticsService.logEvent(
        name: AnalyticsEvents.registration_failed,
        parameters: {
          'phone_number': phoneNumber,
          'error_message': state.appFailure.getErrorMessage(),
          'error_type': state.appFailure.runtimeType.toString(),
          'timestamp': DateTime.now().toIso8601String(),
          'flow_type': 'registration',
          'stage': 'can_register_check',
        },
      );

      _handleFailure(state.appFailure);
      _clearFormFields();
    }

    if (state is CanRegisterSuccess) {
      // Track OTP sent for registration
      analyticsService.logEvent(
        name: AnalyticsEvents.otp_sent,
        parameters: {
          'phone_number': phoneNumber,
          'flow_type': 'registration',
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      context.pushRoute(OtpVerificationPage(
        phoneNumber: phoneNumber,
        registerFormData: RegisterFormData(
          fullName: nameController.text.trim(),
          gender: selectedGender?.genderType ?? '',
          age: double.parse(ageController.text.trim()),
          district: selectedDistrict?.name ?? '',
          mobileNumber: phoneNumber,
        ),
      ));

      _clearFormFields();
    }
  }

  void _clearFormFields() {
    nameController.clear();
    ageController.clear();
    phoneController.clear();
    selectedDistrict = null;
    selectedGender = null;
  }
}
