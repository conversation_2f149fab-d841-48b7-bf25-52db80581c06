import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/animation_direction.dart';
import 'package:hodan_hospital/core/enums/button_state.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/core/utils/helpers/form_validation_helper.dart';
import 'package:hodan_hospital/core/utils/helpers/snack_bar_helper.dart';
import 'package:hodan_hospital/features/authentication/presentation/bloc/authentication_bloc.dart';
import 'package:hodan_hospital/features/authentication/presentation/pages/otp_verification_page.dart';
import 'package:hodan_hospital/features/authentication/presentation/pages/register_page.dart';
import 'package:hodan_hospital/features/main/presentation/pages/main_page.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_item_wrapper.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_button.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_textfield.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/signup_or_login_promt.dart';
import 'package:hodan_hospital/gen/assets.gen.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';

import '../../../../core/config/di/dependency_injection.dart';
import '../../../../core/constants/analytics_events.dart';
import '../../../../core/services/analytics_service.dart';
import '../../../../core/utils/helpers/somali_phone_number_formatter.dart';

class LoginForm extends StatefulWidget {
  const LoginForm({super.key});

  @override
  State<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  final loginFormKey = GlobalKey<FormState>();
  final phoneController = TextEditingController();

  final _updater = ShorebirdUpdater();
  late final bool isUpdaterAvailable;
  final currentTrack = UpdateTrack.stable;
  Patch? currentPatch;

  @override
  void initState() {
    super.initState();
    // _checkForUpdates();
  }

  // check for updates and update if available
  // Future<void> _checkForUpdates() async {
  //   // Check whether Shorebird is available.
  //   setState(() => isUpdaterAvailable = _updater.isAvailable);
  //   print('isUpdaterAvailable is : $isUpdaterAvailable');

  //   // Read the current patch (if there is one.)
  //   // `currentPatch` will be `null` if no patch is installed.
  //   _updater.readCurrentPatch().then((currentPatch) async {
  //     setState(() => currentPatch = currentPatch);
  //     print('Current Path is : $currentPatch');
  //     if (isUpdaterAvailable) {
  //       await _updater.update(track: currentTrack);
  //     }
  //   }).catchError((Object error) {
  //     // If an error occurs, we log it for now.
  //     print('Error reading current patch: $error');
  //   });
  // }

  @override
  void dispose() {
    phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authBloc = context.authenticationBloc;
    final size = context.mediaQuery.size;

    return BlocConsumer<AuthenticationBloc, AuthenticationState>(
      listener: _authListener,
      builder: (context, state) {
        return Stack(
          children: [
            /// Fixed background image in the Top
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              // bottom: size.height * 0.58,
              height: size.height * 0.41,
              child: AnimatedItemWrapper(
                delay: const Duration(milliseconds: 100),
                // delay: Duration.zero,
                animationDirection: AnimationDirection.topToBottom,
                child: Image.asset(
                  // "assets/images/png/login_cover.jpeg",
                  Assets.images.png.loginCover.path,
                  fit: BoxFit.fill,
                  // fit: BoxFit.cover,
                  // fit: BoxFit.fitHeight,
                ),
              ),
            ),

            // Curved White Background
            Positioned.fill(
              child: Padding(
                padding: EdgeInsets.only(top: size.height * 0.2),
                child: CustomPaint(
                  painter: CurvedBackgroundPainter(),
                ),
              ),
            ),

            // Right orange shape
            Positioned(
              right: -50,
              top: size.height * 0.25,
              child: const AnimatedItemWrapper(
                animationDirection: AnimationDirection.rightToLeft,
                child: OrangeShape(isLeft: false),
              ),
            ),

            // Left orange shape
            Positioned(
              left: -50,
              top: size.height * 0.28,
              child: const AnimatedItemWrapper(
                animationDirection: AnimationDirection.leftToRight,
                child: OrangeShape(isLeft: true),
              ),
            ),

            /// Center Logo
            Positioned(
              top: size.height * 0.33,
              left: 0,
              right: 0,
              child:
                  // AnimatedItemWrapper(
                  // delay: const Duration(milliseconds: 300),
                  // animationDirection: AnimationDirection.bottomToTop,
                  // child:
                  Center(
                child: Image.asset(
                  Assets.icon.appIcon.path,
                  width: 200.w,
                  height: 150.h,
                ),
              ),
            ),
            // ),

            // Scrollable form content
            Form(
              key: loginFormKey,
              child: SingleChildScrollView(
                child: Container(
                  height: context.screenHeight,
                  padding: EdgeInsets.only(top: size.height * 0.55),
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20.w),
                        child: _buildPhoneNumberField(),
                      ),
                      SizedBox(height: 30.h),
                      _buildSignInButton(authBloc, state),
                      SizedBox(height: 50.h),
                      _buildSignUpPrompt(
                        isDisabled: state is AuthenticationLoginLoading,
                      ),
                      const Spacer(),
                      _buildFooter(),
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // Widget _buildPhoneNumberField() {
  //   return AnimatedItemWrapper(
  //     delay: const Duration(milliseconds: 200),
  //     child: CustomTextField(
  //       // labelText: "Phone Number",
  //       hintText: '061XXXXXXX',
  //       controller: phoneController,
  //       keyboardType: TextInputType.phone,
  //       labelStyle: context.textTheme.labelMedium?.copyWith(
  //         fontSize: 16.sp,
  //         fontWeight: FontWeight.w600,
  //       ),

  //       hintStyle: context.textTheme.bodySmall?.copyWith(
  //         fontSize: 16.sp,
  //       ),
  //       textInputAction: TextInputAction.done,
  //       validator: (value) =>
  //           FormValidationHelper.validatePhoneNumber(value: value),
  //       prefixIcon: Padding(
  //         padding: EdgeInsets.only(top: 10.h),
  //         child: const FaIcon(FontAwesomeIcons.phone),
  //       ),
  //     ),
  //   );
  // }

  Widget _buildPhoneNumberField() {
    return AnimatedItemWrapper(
      delay: const Duration(milliseconds: 200),
      child: CustomTextField(
        controller: phoneController,
        keyboardType: TextInputType.phone,
        textInputAction: TextInputAction.done,
        hintText: '61XXXXXXX',
        inputFormatters: [
          SomaliPhoneNumberFormatter(allowLeadingZero: true),
        ],
        labelStyle: context.textTheme.labelMedium?.copyWith(
          fontSize: 16.sp,
          fontWeight: FontWeight.w600,
        ),
        hintStyle: context.textTheme.bodySmall?.copyWith(
          fontSize: 16.sp,
        ),
        validator: (value) =>
            FormValidationHelper.validatePhoneNumber(value: value),
        prefixIcon: Padding(
          padding: EdgeInsets.symmetric(horizontal: 10.w),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '+252',
                style: context.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: context.appColors.primaryColor,
                ),
              ),
              VerticalDivider(
                width: 1,
                thickness: 1,
                color: context.appColors.primaryColor.withValues(alpha: 0.5),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSignInButton(
      AuthenticationBloc authBloc, AuthenticationState state) {
    return AnimatedItemWrapper(
      child: CustomButton(
        buttonText: 'Login',
        height: 40.h,
        width: context.screenWidth * 0.6,
        textStyle: context.textTheme.titleMedium?.copyWith(
          fontSize: 20.sp,
          color: context.appColors.whiteColor,
          fontWeight: FontWeight.w600,
        ),
        buttonState: state is AuthenticationLoginLoading
            ? ButtonState.loading
            : ButtonState.normal,
        onTap: () async {
          if (loginFormKey.currentState!.validate()) {
            final mobileNumber = phoneController.text.trim();
            final analyticsService = context.analyticsService;

            // Track login initiation
            await analyticsService.logEvent(
              name: AnalyticsEvents.login_initiated,
              parameters: {
                'phone_number': mobileNumber,
                'method': 'phone_number',
                'timestamp': DateTime.now().toIso8601String(),
                'flow_type': 'login',
              },
            );

            // Also log using the standard login method
            await analyticsService.logLogin(
              method: 'phone_number',
              parameters: {
                'phone_number': mobileNumber,
                'timestamp': DateTime.now().toIso8601String(),
              },
            );

            authBloc.add(LoginEvent(mobileNumber: mobileNumber));
          }
        },
      ),
    );
  }

  Widget _buildSignUpPrompt({required bool isDisabled}) {
    return AnimatedItemWrapper(
      delay: const Duration(milliseconds: 400),
      animationDirection: AnimationDirection.leftToRight,
      child: SignUpOrLoginPromt(
        promt: 'Join us',
        promtType: "Don't have an account?",
        onTap: isDisabled
            ? null
            : () {
                context.pushAndRemoveUntilRoute(const RegisterPage());
              },
      ),
    );
  }

  Widget _buildFooter() {
    return Padding(
      padding: EdgeInsets.only(bottom: 20.h), // Adjust spacing if needed
      child: AnimatedItemWrapper(
        delay: const Duration(milliseconds: 500),
        child: Align(
          alignment: Alignment.bottomCenter,
          child: Text(
            'Powered by Rasiin',
            style: context.textTheme.titleLarge?.copyWith(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  void _authListener(BuildContext context, AuthenticationState state) {
    final analyticsService = context.analyticsService;
    final phoneNumber = phoneController.text.trim();

    if (state is Authenticated) {
      // Track successful authentication (non-blocking)
      analyticsService.logEvent(
        name: AnalyticsEvents.login_success,
        parameters: {
          'phone_number': phoneNumber,
          'method': 'phone_number',
          'timestamp': DateTime.now().toIso8601String(),
          'flow_type': 'login',
          'authentication_complete': 'true',
        },
      );
      context.pushAndRemoveUntilRoute(const MainPage());
    }

    if (state is LoginSuccess) {
      // Track OTP sent successfully (non-blocking)
      analyticsService.logEvent(
        name: AnalyticsEvents.otp_sent,
        parameters: {
          'phone_number': phoneNumber,
          'flow_type': 'login',
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      context.pushRoute(OtpVerificationPage(
        phoneNumber: phoneNumber,
      ));
      phoneController.clear();
    }

    if (state is AuthenticationFailure) {
      final message = state.appFailure.getErrorMessage();

      // Track authentication failure (non-blocking)
      analyticsService.logEvent(
        name: AnalyticsEvents.login_failed,
        parameters: {
          'phone_number': phoneNumber,
          'method': 'phone_number',
          'error_message': message,
          'error_type': state.appFailure.runtimeType.toString(),
          'timestamp': DateTime.now().toIso8601String(),
          'flow_type': 'login',
        },
      );

      AppLogger().warning(
        'Authentication failed: ${state.appFailure.toString()}',
      );

      SnackBarHelper.showErrorSnackBar(
        context,
        message: message,
        actionLabel: 'Hide',
      );
    }

    if (state is LoginFailure) {
      final message = state.appFailure.getErrorMessage();

      // Track login failure (non-blocking)
      analyticsService.logEvent(
        name: AnalyticsEvents.login_failed,
        parameters: {
          'phone_number': phoneNumber,
          'method': 'phone_number',
          'error_message': message,
          'error_type': state.appFailure.runtimeType.toString(),
          'timestamp': DateTime.now().toIso8601String(),
          'flow_type': 'login',
        },
      );

      AppLogger().warning(
        'Login failed: ${state.appFailure.toString()}',
      );

      SnackBarHelper.showErrorSnackBar(
        context,
        message: message,
        actionLabel: 'Hide',
      );
    }
  }
}

// Curved Background Painter
class CurvedBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()..color = Colors.white;
    final Path path = Path();

    // Start at top left
    path.moveTo(0, size.height * 0.1);

    // Left curve
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.1,
      size.width * 0.5, //
      size.height * 0.4, // Adjust this value to control the height of the curve
    );

    // Right curve
    path.quadraticBezierTo(
      size.width * 0.99,
      size.height * 0.1,
      size.width,
      size.height * 0.1,
    );

    // Continue downwards to cover the bottom part
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

/// Orange Shape
class OrangeShape extends StatelessWidget {
  final bool isLeft;
  const OrangeShape({super.key, required this.isLeft});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final imagePath = isLeft
        ? Assets.images.svg.loginRoundedLeftShape
        : Assets.images.svg.loginRoundedRightShape;

    return SizedBox(
      width: size.width * 0.23,
      height: size.height * 0.22,
      child: SvgPicture.asset(
        imagePath,
        fit: BoxFit.fill,
      ),
    );
  }
}
