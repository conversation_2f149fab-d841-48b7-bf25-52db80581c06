import 'package:fpdart/fpdart.dart';
import 'package:hodan_hospital/core/constants/api_end_points.dart';
import 'package:hodan_hospital/core/constants/local_storage_key_constants.dart';
import 'package:hodan_hospital/core/enums/http_method.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/errors/http_error_handler.dart';
import 'package:hodan_hospital/core/network/api_client/dio_api_client.dart';
import 'package:hodan_hospital/core/services/flutter_secure_storage_services.dart';
import 'package:hodan_hospital/core/utils/helpers/request_data.dart';
import 'package:hodan_hospital/core/utils/helpers/response_handler.dart';
import 'package:hodan_hospital/features/shared/data/models/user_model.dart';
import 'package:hodan_hospital/features/user/data/models/district_model.dart';
import 'package:hodan_hospital/features/user/data/models/users_by_phone_model.dart';

abstract class UserRemoteDataSource {
  FutureEitherFailOr<List<UsersByPhoneModel>> getUsersByPhone({
    required String mobileNumber,
    required String doctorName,
  });

  FutureEitherFailOr<String> registerNewPatient({
    required String patFullName,
    required String patGender,
    required double patAge,
    required String patAgeType,
    required String patMobileNumber,
    required String patDistrict,
  });

  FutureEitherFailOr<List<DistrictModel>> getDistricts();
  FutureEitherFailOr<UserModel> getCurrentUser({
    String? token,
  });

  FutureEitherFailOr<String> sendFeedback({
    required String patientId,
    required String feedbackType,
    required double rating,
    required String? appFeedbackCategory,
    required String comments,
    required String appVersion,
    required String deviceInfo,
  });
}

class UserRemoteDataSourceImpl implements UserRemoteDataSource {
  //
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;
  final FlutterSecureStorageServices flutterSecureStorageServices;

  const UserRemoteDataSourceImpl({
    required this.dioApiClient,
    required this.httpErrorHandler,
    required this.flutterSecureStorageServices,
  });

  @override
  FutureEitherFailOr<List<UsersByPhoneModel>> getUsersByPhone({
    required String mobileNumber,
    required String doctorName,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (data) =>
          UsersByPhoneModel.fromJsonList(data as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.getUsersByPhone,
        data: RequestData.json({
          'mobile_number': mobileNumber,
          'doctor_name': doctorName,
        }),
      ),
    );

    return ResponseHandler(response).handleResponseAndExtractData(
      onFailure: (failure) {
        return left(failure);
      },
      onSuccess: (data) {
        //
        return right(data);
      },
    );
  }

  @override
  FutureEitherFailOr<String> registerNewPatient({
    required String patFullName,
    required String patGender,
    required double patAge,
    required String patAgeType,
    required String patMobileNumber,
    required String patDistrict,
  }) async {
    final response = await httpErrorHandler.handleRequest<String>(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.register,
        data: RequestData.json(
          {
            'pat_full_name': patFullName,
            'pat_gender': patGender,
            'pat_age': patAge,
            'pat_age_type': patAgeType,
            'pat_mobile_number': patMobileNumber,
            'pat_district': patDistrict,
          },
        ),
      ),
    );

    return ResponseHandler(response).handleResponse(
      onSuccess: (apiResponse) {
        return right(apiResponse.apiMessage);
      },
      onFailure: (failure) {
        return left(failure);
      },
    );
  }

  @override
  FutureEitherFailOr<List<DistrictModel>> getDistricts() async {
    final response = await httpErrorHandler.handleRequest<List<DistrictModel>>(
      fromJsonT: (data) {
        if (data is List<dynamic>) {
          return DistrictModel.fromJsonList(data);
        }
        return [];
      },
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.getDistricts,
      ),
    );

    return ResponseHandler(response).handleResponseAndExtractData(
      onFailure: (failure) {
        return left(failure);
      },
      onSuccess: (data) {
        return right(data);
      },
    );
  }

  ///
  @override
  FutureEitherFailOr<UserModel> getCurrentUser({
    String? token,
  }) async {
    final pID = await flutterSecureStorageServices.readData(
      key: LocalStorageKeyConstants.userDataKey,
    );
    final response = await httpErrorHandler.handleRequest<UserModel>(
      fromJsonT: (data) => UserModel.fromJson(data as Map<String, dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.getPatientProfile,
        data: RequestData.json({
          'patient_id': pID,
          if (token != null) 'fcm_token': token,
        }),
      ),
    );

    return ResponseHandler(response).handleResponseAndExtractData(
      onFailure: (failure) {
        return left(failure);
      },
      onSuccess: (data) {
        return right(data);
      },
    );
  }

  @override
  FutureEitherFailOr<String> sendFeedback({
    required String patientId,
    required String feedbackType,
    required double rating,
    required String? appFeedbackCategory,
    required String comments,
    required String appVersion,
    required String deviceInfo,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.sendFeedback,
        data: RequestData.json(
          {
            'patient_id': patientId,
            'feedback_type': feedbackType,
            'rating': rating,
            'app_feedback_category': appFeedbackCategory,
            'comments': comments,
            'app_version': appVersion,
            'device_info': deviceInfo,
          },
        ),
      ),
    );

    return response.fold(
      (failure) {
        return left(failure);
      },
      (apiResponse) {
        return right(apiResponse.apiMessage);
      },
    );
  }
}
