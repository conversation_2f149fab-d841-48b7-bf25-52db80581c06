import 'package:fpdart/fpdart.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/features/authentication/data/mappers/auth_mappers.dart';
import 'package:hodan_hospital/features/user/data/datasources/local/user_local_data_source.dart';
import 'package:hodan_hospital/features/user/data/datasources/remote/user_remote_data_source.dart';
import 'package:hodan_hospital/features/user/data/mappers/users_by_phone_mapper.dart';
import 'package:hodan_hospital/features/user/data/mappers/district_mapper.dart';
import 'package:hodan_hospital/features/user/data/models/district_model.dart';
import 'package:hodan_hospital/features/user/data/models/users_by_phone_model.dart';
import 'package:hodan_hospital/features/user/domain/entities/district_entity.dart';
import 'package:hodan_hospital/features/user/domain/entities/users_by_phone_entity.dart';
import 'package:hodan_hospital/features/user/domain/repositories/user_reposiory.dart';
import 'package:hodan_hospital/features/shared/domain/entities/user_entity.dart';

import '../../../../core/services/notification_services.dart';

class UserRepositoryImpl implements UserRepository {
  final UserRemoteDataSource userRemoteDataSource;
  final UserLocalDataSource userLocalDataSource;
  final NotificationService notificationService;

  UserRepositoryImpl({
    required this.userRemoteDataSource,
    required this.userLocalDataSource,
    required this.notificationService,
  });

  @override
  FutureEitherFailOr<UserEntity> getCurrentUser({
    required bool forceFetch,
  }) async {
    // AppLogger().info('getCurrentUser and forceFetch: $forceFetch');

    // Try local cache first if not forcing fetch
    if (!forceFetch) {
      final localResponse = await userLocalDataSource.readUserData();
      if (localResponse.isRight()) {
        return localResponse.map((userModel) {
          final userEntity = AuthMapper.toUserEntity(userModel);
          // AppLogger().info('getCurrentUser from cache: $userEntity');
          return userEntity;
        });
      }
      // If local cache fails, continue to fetch from server instead of returning early
      // AppLogger().info('Local cache failed, falling back to server');
    }

    // Fetch from server (either forced or as fallback from cache failure)
    // AppLogger().info('getCurrentUser from server');
    final token = await notificationService.getToken();
    AppLogger().info('FCM Token: $token');

    final remoteResponse = await userRemoteDataSource.getCurrentUser(
      token: token,
    );

    return remoteResponse.fold((failure) async {
      // Server failed, try local cache as last resort
      final localResponse = await userLocalDataSource.readUserData();
      if (localResponse.isRight()) {
        // AppLogger().info('getCurrentUser from cache as fallback: $localResponse');
        return localResponse.map(
          (user) {
            final userEntity = AuthMapper.toUserEntity(user);
            return userEntity;
          },
        );
      }
      // Both server and cache failed
      // AppLogger().info('getCurrentUser failed from both server and cache: $failure');
      return left(failure);
    }, (userModel) async {
      // Server success - save to cache and return
      final userEntity = AuthMapper.toUserEntity(userModel);
      await userLocalDataSource.saveUserData(userModel: userModel);
      // AppLogger().info('getCurrentUser from server: $userEntity');
      return right(userEntity);
    });
  }

  @override
  FutureEitherFailOr<List<UsersByPhoneEntity>> getUsersByPhone({
    required String mobileNumber,
    required String doctorName,
    required bool forceFetch,
  }) async {
    try {
      // if (!forceFetch) {
      //   final cachedResponse = await _getCachedUsesByPhone();
      //   if (cachedResponse.isRight()) {
      //     return cachedResponse;
      //   }
      // }
      final remoteResponse = await _fetchUsersByPhoneFromServer(
        mobileNumber: mobileNumber,
        doctorName: doctorName,
      );
      return remoteResponse.fold(
        (failure) async {
          // final cachedResponse = await _getCachedUsesByPhone();
          // if (cachedResponse.isRight()) {
          //   return cachedResponse;
          // }
          return left(failure);
        },
        (bannerModels) async {
          // await userLocalDataSource.saveUsersByPhone(users: bannerModels);
          final bannersEntity =
              UsersByPhoneMapper.modelListToEntityList(bannerModels);
          return right(bannersEntity);
        },
      );
    } catch (error, stackTrace) {
      AppLogger().error(
        "Error fetching usersByPhone from ${forceFetch ? 'Server' : 'Cache'}  error: ${error.toString()}",
        error: error,
        stackTrace: stackTrace,
      );
      return left(
        UnexpectedFailure(
          message:
              'Failed to load usersByPhone from ${forceFetch ? 'server' : 'cache'} error: ${error.toString()}',
          stackTrace: stackTrace,
        ),
      );
    }
  }

  // FutureEitherFailOr<List<UsersByPhoneEntity>> _getCachedUsesByPhone() async {
  //   final cachedResponse = await userLocalDataSource.readUsersByPhone();
  //   return cachedResponse.map(
  //     (bannerModels) {
  //       final bannersEntity =
  //           UsersByPhoneMapper.modelListToEntityList(bannerModels);
  //       return bannersEntity;
  //     },
  //   );
  // }

  FutureEitherFailOr<List<UsersByPhoneModel>> _fetchUsersByPhoneFromServer({
    required String mobileNumber,
    required String doctorName,
  }) async {
    return await userRemoteDataSource.getUsersByPhone(
      mobileNumber: mobileNumber,
      doctorName: doctorName,
    );
  }

  @override
  FutureEitherFailOr<String> registerNewPatient({
    required String patFullName,
    required String patGender,
    required double patAge,
    required String patAgeType,
    required String patMobileNumber,
    required String patDistrict,
  }) async {
    return await userRemoteDataSource.registerNewPatient(
      patFullName: patFullName,
      patGender: patGender,
      patAge: patAge,
      patAgeType: patAgeType,
      patMobileNumber: patMobileNumber,
      patDistrict: patDistrict,
    );
  }

  @override
  FutureEitherFailOr<List<DistrictEntity>> getDistricts({
    required bool forceFetch,
  }) async {
    try {
      // if (!forceFetch) {
      //   final cachedResponse = await _getCachedDistricts();
      //   if (cachedResponse.isRight()) {
      //     return cachedResponse;
      //   }
      // }
      final remoteResponse = await _fetchDistrictsFromServer();
      return remoteResponse.fold(
        (failure) async {
          // final cachedResponse = await _getCachedDistricts();
          // if (cachedResponse.isRight()) {
          //   return cachedResponse;
          // }
          return left(failure);
        },
        (districtModels) async {
          // await userLocalDataSource.saveDistricts(districts: districtModels);
          final districtsEntity =
              DistrictMapper.modelListToEntityList(districtModels);
          return right(districtsEntity);
        },
      );
    } catch (error, stackTrace) {
      AppLogger().error(
        "Error fetching districts from ${forceFetch ? 'Server' : 'Cache'}  error: ${error.toString()}",
        error: error,
        stackTrace: stackTrace,
      );
      return left(
        UnexpectedFailure(
          message:
              'Failed to load districts from ${forceFetch ? 'server' : 'cache'} error: ${error.toString()}',
          stackTrace: stackTrace,
        ),
      );
    }
  }

  // FutureEitherFailOr<List<DistrictEntity>> _getCachedDistricts() async {
  //   final cachedResponse = await userLocalDataSource.readDistricts();
  //   return cachedResponse.map(
  //     (districtModels) {
  //       final districtsEntity =
  //           DistrictMapper.modelListToEntityList(districtModels);
  //       return districtsEntity;
  //     },
  //   );
  // }

  FutureEitherFailOr<List<DistrictModel>> _fetchDistrictsFromServer() async {
    return await userRemoteDataSource.getDistricts();
  }

  @override
  FutureEitherFailOr<String> sendFeedback({
    required String patientId,
    required String feedbackType,
    required double rating,
    required String? appFeedbackCategory,
    required String comments,
    required String appVersion,
    required String deviceInfo,
  }) async {
    final patientIdResponse = await userLocalDataSource.getPatientId();
    return await patientIdResponse.fold(
      (failure) async {
        return await userRemoteDataSource.sendFeedback(
          patientId: patientId,
          feedbackType: feedbackType,
          rating: rating,
          appFeedbackCategory: appFeedbackCategory,
          comments: comments,
          appVersion: appVersion,
          deviceInfo: deviceInfo,
        );
      },
      (pID) async {
        return await userRemoteDataSource.sendFeedback(
          patientId: pID,
          feedbackType: feedbackType,
          rating: rating,
          appFeedbackCategory: appFeedbackCategory,
          comments: comments,
          appVersion: appVersion,
          deviceInfo: deviceInfo,
        );
      },
    );
  }
}
