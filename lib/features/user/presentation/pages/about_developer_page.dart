import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/loading_type.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_item_wrapper.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_app_bar.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_list_grid_view.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/utils/helpers/url_laucher_helper.dart';
import '../../../../gen/assets.gen.dart';

class AboutDeveloperPage extends StatelessWidget {
  const AboutDeveloperPage({super.key});

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;

    return Scaffold(
      backgroundColor: appColors.backgroundColor,
      body: Column(
        children: [
          const CustomAppBar(
            title: 'About Developer',
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Header Section with Company Branding
                  _buildHeaderSection(context),

                  // Main Content
                  Padding(
                    padding: EdgeInsets.all(20.w),
                    child: Column(
                      children: [
                        // Company Overview Card
                        _buildCompanyOverviewCard(
                          context,
                        ),
                        SizedBox(height: 24.h),

                        // Services Grid
                        _buildServicesGrid(context),
                        SizedBox(height: 24.h),

                        // Experience & Achievements Card
                        _buildExperienceCard(context),
                        SizedBox(height: 24.h),

                        // Contact & Website Section
                        _buildContactSection(context),
                        SizedBox(height: 40.h),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderSection(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    return SizedBox(
      width: double.infinity,
      // decoration: BoxDecoration(
      //   gradient: LinearGradient(
      //     begin: Alignment.topLeft,
      //     end: Alignment.bottomRight,
      //     colors: [
      //       appColors.primaryColor,
      //       appColors.primaryColor.withValues(alpha: 0.8),
      //     ],
      //   ),
      // ),
      child: SafeArea(
        bottom: false,
        child: Padding(
          padding: EdgeInsets.fromLTRB(20.w, 0.h, 20.w, 40.h),
          child: Column(
            children: [
              AnimatedItemWrapper(
                delay: const Duration(milliseconds: 200),
                child:
                    //  Container(
                    // width: 100.w,
                    // height: 100.h,
                    //   decoration: const BoxDecoration(
                    // color: appColors.whiteColor,
                    // shape: BoxShape.circle,
                    // boxShadow: [
                    //   BoxShadow(
                    //     color: Colors.black.withValues(alpha: 0.2),
                    //     blurRadius: 15,
                    //     offset: const Offset(0, 8),
                    //   ),
                    // ],
                    // ),
                    // child: Icon(
                    //   FontAwesomeIcons.code,
                    //   size: 50.sp,
                    //   color: appColors.primaryColor,
                    // ),
                    // child:
                    Image.asset(
                  Assets.icon.rasiinLogo.path,
                  fit: BoxFit.cover,
                  width: 150.w,
                  height: 150.h,
                  errorBuilder: (context, error, stackTrace) {
                    // print('Error loading image: $error, $stackTrace');
                    return const Icon(Icons.error, color: Colors.red);
                  },
                  // ),
                ),
              ),
              SizedBox(height: 20.h),
              AnimatedItemWrapper(
                delay: const Duration(milliseconds: 400),
                child: Text(
                  'Rasiin Tech',
                  style: textTheme.headlineMedium?.copyWith(
                    // color: appColors.whiteColor,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              SizedBox(height: 8.h),
              AnimatedItemWrapper(
                delay: const Duration(milliseconds: 600),
                child: Text(
                  'Transforming businesses with innovative technology solutions since 2016',
                  style: textTheme.bodyLarge?.copyWith(
                      // color: appColors.whiteColor.withValues(alpha: 0.9),
                      ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCompanyOverviewCard(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    return AnimatedItemWrapper(
      delay: const Duration(milliseconds: 800),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(
          color: appColors.cardColor,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: appColors.dividerColor.withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color: appColors.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Icon(
                    FontAwesomeIcons.building,
                    color: appColors.primaryColor,
                    size: 24.sp,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Company Overview',
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: appColors.textColor,
                        ),
                      ),
                      Text(
                        'Founded in 2016',
                        style: textTheme.bodyMedium?.copyWith(
                          color: appColors.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.h),
            Text(
              'We are a leading technology company specializing in comprehensive ERP (Enterprise Resource Planning) solutions. Our mission is to transform businesses across various industries by providing tailored digital solutions that streamline operations, enhance efficiency, and drive growth.',
              style: textTheme.bodyMedium?.copyWith(
                color: appColors.textColor,
                height: 1.6,
              ),
              textAlign: TextAlign.justify,
            ),
            SizedBox(height: 16.h),
            Text(
              'Our expertise spans multiple sectors including healthcare, education, and various other industries that require robust ERP systems. We believe in creating solutions that are not just functional, but also user-friendly and scalable.',
              style: textTheme.bodyMedium?.copyWith(
                color: appColors.textColor,
                height: 1.6,
              ),
              textAlign: TextAlign.justify,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServicesGrid(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    final services = [
      {
        'icon': FontAwesomeIcons.hospital,
        'title': 'Hospital Management',
        'desc': 'Complete healthcare ERP solutions'
      },
      {
        'icon': FontAwesomeIcons.graduationCap,
        'title': 'School Management',
        'desc': 'Educational institution systems'
      },
      {
        'icon': FontAwesomeIcons.mobile,
        'title': 'Mobile Apps',
        'desc': 'Cross-platform mobile solutions'
      },
      {
        'icon': FontAwesomeIcons.globe,
        'title': 'Web Development',
        'desc': 'Modern web applications'
      },
      {
        'icon': FontAwesomeIcons.gears,
        'title': 'Custom ERP',
        'desc': 'Tailored business solutions'
      },
      {
        'icon': FontAwesomeIcons.code,
        'title': 'Frappe Development',
        'desc': 'Framework-based applications'
      },
    ];

    return AnimatedItemWrapper(
      delay: const Duration(milliseconds: 1000),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(
          color: appColors.cardColor,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: appColors.dividerColor.withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Our Services',
              style: textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                // color: appColors.textColor,
              ),
            ),
            SizedBox(height: 20.h),
            CustomListGridView<Map<String, dynamic>>(
              items: services,
              isLoading: false,
              isEmpty: services.isEmpty,
              contentType: LoadingType.gridView,
              gridCrossAxisCount: 2,
              childAspectRatio: 1.5,
              showFooter: false,
              physics: const NeverScrollableScrollPhysics(),
              padding: EdgeInsets.zero,
              itemBuilder: (context, service) {
                return Container(
                  padding: EdgeInsets.all(8.w), // Reduced padding
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        appColors.primaryColor.withValues(alpha: 0.05),
                        appColors.primaryColor.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(12.r),
                    border: Border.all(
                      color: appColors.primaryColor.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      // Icon section
                      Flexible(
                        flex: 2,
                        child: Icon(
                          service['icon'] as IconData,
                          color: appColors.primaryColor,
                          size: 24.sp, // Reduced icon size
                        ),
                      ),

                      // Title section
                      Flexible(
                        flex: 2,
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 4.w),
                          child: Text(
                            service['title'] as String,
                            style: textTheme.bodyLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: appColors.textColor,
                              fontSize: 8.sp, // Fixed font size
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),

                      SizedBox(height: 2.h),

                      // Description section
                      Flexible(
                        flex: 2,
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 2.w),
                          child: Text(
                            service['desc'] as String,
                            style: TextStyle(
                              color: appColors.subtextColor,
                              fontSize: 9.sp, // Very small font size
                              height: 1.2,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExperienceCard(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    return AnimatedItemWrapper(
      delay: const Duration(milliseconds: 1400),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              appColors.primaryColor.withValues(alpha: 0.05),
              appColors.primaryColor.withValues(alpha: 0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: appColors.primaryColor.withValues(alpha: 0.2),
          ),
        ),
        child: Column(
          children: [
            Icon(
              FontAwesomeIcons.trophy,
              color: appColors.primaryColor,
              size: 40.sp,
            ),
            SizedBox(height: 16.h),
            Text(
              'Experience & Achievements',
              style: textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: appColors.textColor,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 20.h),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(context, '8+', 'Years Experience'),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildStatCard(context, '100+', 'Projects Delivered'),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(context, '50+', 'Happy Clients'),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildStatCard(context, '24/7', 'Support'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(BuildContext context, String number, String label) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: appColors.cardColor,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: appColors.dividerColor.withValues(alpha: 0.2),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            number,
            style: textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: appColors.primaryColor,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            label,
            style: textTheme.bodySmall?.copyWith(
                // color: appColors.subtextColor,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildContactSection(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    return AnimatedItemWrapper(
      delay: const Duration(milliseconds: 1600),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(
          color: appColors.cardColor,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: appColors.dividerColor.withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Text(
              'Get In Touch',
              style: textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: appColors.textColor,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Ready to transform your business with our ERP solutions?',
              style: textTheme.bodyMedium?.copyWith(
                color: appColors.subtextColor,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24.h),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _launchWhatsApp(context),
                    icon: Icon(
                      FontAwesomeIcons.whatsapp,
                      size: 18.sp,
                    ),
                    label: const Text('WhatsApp'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF25D366),
                      foregroundColor: appColors.whiteColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                    ),
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _launchEmail(context),
                    icon: Icon(
                      FontAwesomeIcons.envelope,
                      size: 18.sp,
                    ),
                    label: const Text('Contact Us'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: appColors.primaryColor,
                      side: BorderSide(color: appColors.primaryColor),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _launchWhatsApp(BuildContext context) async {
    const phoneNumber = '+252618226206';
    const message = 'Hello! I\'m interested in your ERP solutions.';
    // final url =
    // 'https://wa.me/$phoneNumber?text=${Uri.encodeComponent(message)}';
    // final Uri uri = Uri.parse(url);
    // if (await canLaunchUrl(uri)) {
    //   await launchUrl(uri, mode: LaunchMode.externalApplication);
    // }
    await UrlLauncherHelper().launchWhatsApp(
      context: context,
      phoneNumber: phoneNumber,
      message: message,
    );
  }

  Future<void> _launchEmail(BuildContext context) async {
    // const email = 'mailto:<EMAIL>?subject=ERP Solutions Inquiry';
    // final Uri uri = Uri.parse(email);
    // if (await canLaunchUrl(uri)) {
    //   await launchUrl(uri);
    // }
    await UrlLauncherHelper().launchEmail(
      context: context,
      toEmail: '<EMAIL>',
      subject: 'ERP Solutions Inquiry',
      body: 'Hello! I\'m interested in your ERP solutions.',
    );
  }
}
