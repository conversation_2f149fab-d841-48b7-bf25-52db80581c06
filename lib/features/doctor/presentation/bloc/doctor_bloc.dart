import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/params/no_params.dart';
import 'package:hodan_hospital/core/utils/helpers/bloc_helper.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/banner_entity.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/doctor_department_entity.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/doctor_entity.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/recent_search_entity.dart';
import 'package:hodan_hospital/features/doctor/domain/params/get_banners_params.dart';
import 'package:hodan_hospital/features/doctor/domain/params/get_doctor_departments_params.dart';
import 'package:hodan_hospital/features/doctor/domain/params/get_doctors_by_department_params.dart';
import 'package:hodan_hospital/features/doctor/domain/params/get_doctors_params.dart';
import 'package:hodan_hospital/features/doctor/domain/params/save_recent_search_params.dart';
import 'package:hodan_hospital/features/doctor/domain/params/search_doctor_params.dart';
import 'package:hodan_hospital/features/doctor/domain/usecases/get_banners_use_case.dart';
import 'package:hodan_hospital/features/doctor/domain/usecases/get_doctor_departments_use_case.dart';
import 'package:hodan_hospital/features/doctor/domain/usecases/get_doctors_by_department_use_case.dart';
import 'package:hodan_hospital/features/doctor/domain/usecases/get_doctors_use_case.dart';
import 'package:hodan_hospital/features/doctor/domain/usecases/get_recent_search_use_case.dart';
import 'package:hodan_hospital/features/doctor/domain/usecases/save_recent_search_use_case.dart';
import 'package:hodan_hospital/features/doctor/domain/usecases/search_doctor_use_case.dart';

part 'doctor_event.dart';
part 'doctor_state.dart';

class DoctorBloc extends Bloc<DoctorEvent, DoctorState> {
  final GetDoctorsUseCase getDoctorsUseCase;
  final SearchDoctorUseCase searchDoctorUseCase;
  final GetBannersUseCase getBannersUseCase;
  final GetRecentSearchUseCase getRecentSearchUseCase;
  final SaveRecentSearchUseCase saveRecentSearchUseCase;
  final GetDoctorDepartmentsUseCase getDoctorDepartmentsUseCase;
  final GetDoctorsByDepartmentUseCase getDoctorsByDepartmentUseCase;
  DoctorBloc({
    required this.getDoctorsUseCase,
    required this.searchDoctorUseCase,
    required this.getBannersUseCase,
    required this.getRecentSearchUseCase,
    required this.saveRecentSearchUseCase,
    required this.getDoctorDepartmentsUseCase,
    required this.getDoctorsByDepartmentUseCase,
  }) : super(DoctorInitial()) {
    // 🟢 Handle get doctors
    on<GetDoctors>(_onGetDoctors, transformer: BlocHelper.debounceHelper());
    // 🟢 Handle search docto
    on<SearchDoctor>(_onSearchDoctor);

    // 🟢 Handle get banners
    on<GetBanners>(_onGetBanners);

    // 🟢 Handle get recent search
    on<GetRecentSearch>(_onGetRecentSearch);
    // 🟢 Handle save recent search
    on<SaveRecentSearch>(_onSaveRecentSearch);

    // 🟢 Handle get doctor departments
    on<GetDoctorDepartments>(_onGetDoctorDepartments);

    // 🟢 Handle get doctors by department
    on<GetDoctorsByDepartment>(_onGetDoctorsByDepartment);
  }

  List<DoctorEntity> _doctors = [];
  List<DoctorEntity> get doctors => _doctors;
  List<DoctorEntity> _filteredDoctors = [];
  List<DoctorEntity> get filteredDoctors => _filteredDoctors;

  List<BannerEntity> _homeBanners = [];
  List<BannerEntity> get homeBanners => _homeBanners;
  List<BannerEntity> _serviceBanners = [];
  List<BannerEntity> get serviceBanners => _serviceBanners;
  List<BannerEntity> _announcementBanners = [];
  List<BannerEntity> get announcementBanners => _announcementBanners;

  List<RecentSearchEntity> _recentSearches = [];
  List<RecentSearchEntity> get recentSearches => _recentSearches;

  List<DoctorDepartmentEntity> _doctorDepartments = [];
  List<DoctorDepartmentEntity> get doctorDepartments => _doctorDepartments;

  List<DoctorEntity> _doctorsByDepartment = [];
  List<DoctorEntity> get doctorsByDepartment => _doctorsByDepartment;

  // 🟢 Handle get doctors
  Future<void> _onGetDoctors(
      GetDoctors event, Emitter<DoctorState> emit) async {
    await BlocHelper.handleEventAndEmit<List<DoctorEntity>, DoctorState>(
      emit: emit,
      loadingState: DoctorLoading(),
      callUseCase: getDoctorsUseCase(
          params: GetDoctorsParams(forceFetch: event.forceFetch)),
      onSuccess: (allDoctors) {
        _doctors = allDoctors;
        _filteredDoctors = List.from(_doctors);
        return DoctorLoaded(doctors: allDoctors);
      },
      onFailure: (failure) => DoctorFailure(failure: failure),
    );
  }

  // 🟢 Handle search doctor
  Future<void> _onSearchDoctor(
      SearchDoctor event, Emitter<DoctorState> emit) async {
    await BlocHelper.handleEventAndEmit<List<DoctorEntity>, DoctorState>(
      emit: emit,
      loadingState: DoctorFilterLoading(),
      callUseCase: searchDoctorUseCase(
        params: SearchDoctorParams(
          query: event.query,
          doctors: _doctors,
        ),
      ),
      onSuccess: (filteredDctrs) {
        _filteredDoctors = filteredDctrs;
        return DoctorFiltered(doctors: _filteredDoctors);
      },
      onFailure: (failure) => DoctorFailure(failure: failure),
    );
  }

  // 🟢 Handle get banners
  Future<void> _onGetBanners(
      GetBanners event, Emitter<DoctorState> emit) async {
    await BlocHelper.handleEventAndEmit<List<BannerEntity>, DoctorState>(
      emit: emit,
      loadingState: BannersLoading(),
      callUseCase: getBannersUseCase(
          params: GetBannersParams(forceFetch: event.forceFetch)),
      onSuccess: (allBanners) {
        // _banners = allBanners;
        _homeBanners = allBanners
            .where((banner) => banner.bannerType == 'Banner')
            .toList();
        _serviceBanners = allBanners
            .where((banner) => banner.bannerType == 'Service')
            .toList();

            _announcementBanners = allBanners
            .where((banner) => banner.bannerType == 'Announcement')
            .toList();


        return BannersLoaded(banners: _homeBanners);
      },
      onFailure: (failure) => BannerFailure(failure: failure),
    );
  }

  // 🟢 Handle get recent search
  Future<void> _onGetRecentSearch(
      GetRecentSearch event, Emitter<DoctorState> emit) async {
    await BlocHelper.handleEventAndEmit<List<RecentSearchEntity>, DoctorState>(
      emit: emit,
      loadingState: RecentSearchesLoading(),
      callUseCase: getRecentSearchUseCase(params: NoParams()),
      onSuccess: (allRecentSearches) {
        _recentSearches = allRecentSearches;
        return RecentSearchesLoaded(recentSearches: allRecentSearches);
      },
      onFailure: (failure) => DoctorFailure(failure: failure),
    );
  }

  // 🟢 Handle save recent search
  Future<void> _onSaveRecentSearch(
      SaveRecentSearch event, Emitter<DoctorState> emit) async {
    await BlocHelper.handleEventAndEmit<void, DoctorState>(
      emit: emit,
      loadingState: RecentSearchSaving(),
      callUseCase: saveRecentSearchUseCase(
        params: SaveRecentSearchParams(
          recentSearchEntity: event.recentSearchEntity,
        ),
      ),
      onSuccess: (_) => RecentSearchSaved(),
      onFailure: (failure) => DoctorFailure(failure: failure),
    );
  }

  // 🟢 Handle get doctor departments
  Future<void> _onGetDoctorDepartments(
      GetDoctorDepartments event, Emitter<DoctorState> emit) async {
    await BlocHelper.handleEventAndEmit<List<DoctorDepartmentEntity>,
        DoctorState>(
      emit: emit,
      loadingState: DoctorDepartmentsLoading(),
      callUseCase: getDoctorDepartmentsUseCase(
        params: GetDoctorDepartmentsParams(forceFetch: event.forceFetch),
      ),
      onSuccess: (allDoctorDepartments) {
        _doctorDepartments = allDoctorDepartments;
        return DoctorDepartmentsLoaded(doctorDepartments: allDoctorDepartments);
      },
      onFailure: (failure) => DoctorFailure(failure: failure),
    );
  }

  // 🟢 Handle get doctors by department
  Future<void> _onGetDoctorsByDepartment(
      GetDoctorsByDepartment event, Emitter<DoctorState> emit) async {
    await BlocHelper.handleEventAndEmit<List<DoctorEntity>, DoctorState>(
      emit: emit,
      loadingState: DoctorsByDepartmentLoading(),
      callUseCase: getDoctorsByDepartmentUseCase(
        params: GetDoctorsByDepartmentParams(
          department: event.department,
          forceFetch: event.forceFetch,
        ),
      ),
      onSuccess: (allDoctorsByDepartment) {
        _doctorsByDepartment = allDoctorsByDepartment;
        return DoctorsByDepartmentLoaded(doctors: allDoctorsByDepartment);
      },
      onFailure: (failure) => DoctorFailure(failure: failure),
    );
  }
}
