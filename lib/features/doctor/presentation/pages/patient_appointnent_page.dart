import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/button_state.dart';
import 'package:hodan_hospital/core/enums/image_type.dart';
import 'package:hodan_hospital/core/enums/loading_type.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/core/utils/helpers/bottom_sheet_helper.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/doctor_entity.dart';
import 'package:hodan_hospital/features/doctor/presentation/pages/patient_appointment_confirm_page.dart';
import 'package:hodan_hospital/features/shared/data/models/gender.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_item_wrapper.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_app_bar.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_button.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_image_picker_card.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_list_grid_view.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_textfield.dart';
import 'package:hodan_hospital/features/user/domain/entities/district_entity.dart';
import 'package:hodan_hospital/features/user/domain/entities/users_by_phone_entity.dart';
import 'package:hodan_hospital/features/user/presentation/bloc/user_bloc.dart';
import 'package:hodan_hospital/core/utils/helpers/snack_bar_helper.dart';

class PatientAppointnentPage extends StatefulWidget {
  final DoctorEntity doctor;
  const PatientAppointnentPage({super.key, required this.doctor});

  @override
  State<PatientAppointnentPage> createState() => _PatientAppointnentPageState();
}

class _PatientAppointnentPageState extends State<PatientAppointnentPage> {
  final _scrollController = ScrollController();
  final appointmentFormKey = GlobalKey<FormState>();
  final TextEditingController _pIDController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController patientNameController = TextEditingController();
  final TextEditingController ageController = TextEditingController();

  UsersByPhoneEntity? selectedUser;
  Gender? selectedGender;
  DistrictEntity? selectedDistrict;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await Future.wait([
        _loadUsers(),
        _loadDistricts(),
      ]);
    });
  }

  Future<void> _loadUsers({bool forceFetch = false}) async {
    final currentUser = context.userBloc.currentUser;
    final phoneNumber = currentUser?.phoneNumber ?? '';
    final familyMembers = context.userBloc.usersByPhone;
    final canRefreshFamilyMembers = forceFetch || familyMembers.isEmpty;

    if (canRefreshFamilyMembers) {
      context.userBloc.add(
        GetUsersByPhoneNumberEvent(
          phoneNumber: phoneNumber,
          doctorName: widget.doctor.name,
        ),
      );

      await context.userBloc.stream
          .firstWhere(
        (state) => state is UsersLoaded || state is UserFailure,
      )
          .then((_) {
        if (mounted) {
          final firstUser = context.userBloc.usersByPhone.firstOrNull;
          if (firstUser != null) {
            setState(() {
              selectedUser = firstUser;
              _pIDController.text = selectedUser?.pID ?? '';
              _nameController.text = selectedUser?.firstName ?? '';
            });
          }
        }
      });
    }
  }

  Future<void> _loadDistricts({bool forceFetch = false}) async {
    final districts = context.userBloc.districts;
    final canRefreshDistricts = forceFetch || districts.isEmpty;
    if (canRefreshDistricts) {
      context.userBloc.add(const GetDistrictsEvent(forceFetch: true));
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _pIDController.dispose();
    _nameController.dispose();
    patientNameController.dispose();
    ageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final colors = context.appColors;
    final userBloc = context.userBloc;
    final currentUserPhone = userBloc.currentUser?.phoneNumber ?? '';

    return BlocConsumer<UserBloc, UserState>(
      listener: (context, state) {
        if (state is UserRegistrationLoading) {
          context.dialogCubit.showLoadingDialog();
        }

        if (state is UserFailure) {
          context.dialogCubit.closeDialog();
          SnackBarHelper.showErrorSnackBar(
            context,
            message: state.appFailure.getErrorMessage(),
          );
        }

        if (state is UserRegistered) {
          context.dialogCubit.closeDialog();
          userBloc.add(GetUsersByPhoneNumberEvent(
            phoneNumber: currentUserPhone,
            doctorName: widget.doctor.name,
          ));
          SnackBarHelper.showSuccessSnackBar(context, message: state.message);
          patientNameController.clear();
          ageController.clear();
          context.popRoute();
        }
      },
      builder: (context, state) {
        final usersByPhone = userBloc.usersByPhone;
        final isLoading = state is UserRegistrationLoading;

        return Scaffold(
          body: Form(
            key: appointmentFormKey,
            child: Stack(
              children: [
                CustomScrollView(
                  controller: _scrollController,
                  slivers: [
                    // App Bar
                    SliverAppBar(
                      title: Text('Patient',
                          style: textTheme.titleLarge?.copyWith(
                            color: colors.whiteColor,
                          )),
                      actionsIconTheme: IconThemeData(
                        color: colors.whiteColor,
                      ),
                      leading: IconButton(
                        icon: Icon(Icons.arrow_back_ios,
                            color: colors.whiteColor),
                        onPressed: () => context.popRoute(),
                      ),
                      pinned: true,
                      floating: true,
                      actions: [
                        _buildAddPatientButton(context, isLoading, colors),
                        SizedBox(width: 12.w),
                      ],
                    ),

                    // Main Content
                    SliverPadding(
                      padding: EdgeInsets.symmetric(horizontal: 20.w),
                      sliver: SliverList(
                        delegate: SliverChildListDelegate([
                          SizedBox(height: 16.h),
                          _buildDoctorInfoCard(),
                          SizedBox(height: 24.h),
                          _buildAppointmentForSection(textTheme),
                          SizedBox(height: 16.h),
                          _buildPatientInfoFields(),
                          SizedBox(height: 24.h),
                          _buildPatientSelectionTitle(textTheme),
                        ]),
                      ),
                    ),

                    // Patient List
                    SliverPadding(
                      padding: EdgeInsets.symmetric(horizontal: 20.w),
                      sliver: SliverList(
                        delegate: SliverChildBuilderDelegate(
                          (context, index) {
                            if (state is UserLoading) {
                              return const Center(
                                  child: CircularProgressIndicator());
                            }

                            if (usersByPhone.isEmpty) {
                              return _buildEmptyState(context);
                            }

                            return _buildPatientList(usersByPhone, context);
                          },
                          childCount: 1,
                        ),
                      ),
                    ),

                    // Bottom padding for the fixed button
                    SliverPadding(
                      padding: EdgeInsets.only(bottom: 100.h),
                    ),
                  ],
                ),

                // Fixed Book Now Button
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: _buildBottomButton(context),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAddPatientButton(BuildContext context, bool isLoading, colors) {
    return InkWell(
      onTap: () => _showAddPatientBottomSheet(context, isLoading),
      child: Container(
        height: 32.h,
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        decoration: BoxDecoration(
          color: colors.whiteColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(color: colors.whiteColor.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.person_add_rounded,
                size: 16.w, color: colors.whiteColor),
            SizedBox(width: 4.w),
            Text('New Patient',
                style: TextStyle(
                  color: colors.whiteColor,
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildDoctorInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Doctor Information',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: context.appColors.primaryColor,
                )),
            SizedBox(height: 12.h),
            Row(
              children: [
                CustomImagePickerCard(
                  imageUrl: widget.doctor.image,
                  userName: widget.doctor.name,
                  imageType: ImageType.profile,
                  radius: 30,
                ),
                SizedBox(width: 15.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(widget.doctor.name,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                          )),
                      SizedBox(height: 4.h),
                      Text(widget.doctor.department,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.grey[600],
                          )),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppointmentForSection(TextTheme textTheme) {
    return Text('Appointment For:',
        style: textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
        ));
  }

  Widget _buildPatientInfoFields() {
    return Column(
      children: [
        AnimatedItemWrapper(
          delay: const Duration(milliseconds: 600),
          child: CustomTextField(
            isReadOnly: true,
            controller: _pIDController,
            labelText: 'Patient ID',
            prefixIcon: Icon(Icons.credit_card,
                size: 20.w, color: context.appColors.primaryColor),
          ),
        ),
        SizedBox(height: 16.h),
        AnimatedItemWrapper(
          delay: const Duration(milliseconds: 700),
          child: CustomTextField(
            isReadOnly: true,
            controller: _nameController,
            labelText: 'Patient Name',
            prefixIcon: Icon(Icons.person,
                size: 20.w, color: context.appColors.primaryColor),
          ),
        ),
      ],
    );
  }

  Widget _buildPatientSelectionTitle(TextTheme textTheme) {
    return Text('Who is the patient?',
        style: textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.bold,
        ));
  }

  Widget _buildEmptyState(BuildContext context) {
    final colors = context.appColors;
    final textTheme = context.textTheme;
    return Container(
      height: 200.h,
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.people_alt_outlined,
              size: 48.w, color: colors.subtextColor.withValues(alpha: 0.5)),
          SizedBox(height: 16.h),
          Text('No patients found',
              style: textTheme.bodyLarge?.copyWith(
                color: colors.subtextColor,
              )),
          SizedBox(height: 8.h),
          Text(
            'Add a new patient or pull down to refresh',
            style: textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPatientList(
    List<UsersByPhoneEntity> users,
    BuildContext context,
  ) {
    final colors = context.appColors;
    final textTheme = context.textTheme;
    return RefreshIndicator(
      onRefresh: () => _loadUsers(forceFetch: true),
      child: Container(
        height: 250.h,
        margin: EdgeInsets.only(bottom: 20.h),
        child: ListView.builder(
          physics: const AlwaysScrollableScrollPhysics(),
          itemCount: users.length,
          itemBuilder: (context, index) {
            final user = users[index];
            final isSelected = selectedUser == user;
            return Card(
              elevation: 2,
              margin: EdgeInsets.only(bottom: 12.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
                side: BorderSide(
                  color: isSelected ? colors.primaryColor : colors.dividerColor,
                  width: isSelected ? 1.5 : 0.5,
                ),
              ),
              child: ListTile(
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 8.h,
                ),
                leading: Container(
                  width: 48.w,
                  height: 48.h,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected
                          ? colors.primaryColor
                          : colors.dividerColor,
                      width: 1.5,
                    ),
                  ),
                  child: CustomImagePickerCard(
                    imageUrl: user.patientImage,
                    userName: user.firstName,
                    imageType: ImageType.profile,
                    radius: 22,
                  ),
                ),
                title: Text(user.firstName, style: textTheme.bodyLarge),
                subtitle: Text('ID: ${user.pID}', style: textTheme.bodySmall),
                trailing: isSelected
                    ? Icon(Icons.check_circle, color: colors.primaryColor)
                    : null,
                onTap: () => _selectPatient(user),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildBottomButton(BuildContext context) {
    final colors = context.appColors;
    // final textTheme = context.textTheme;
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: colors.backgroundColor,
        boxShadow: [
          BoxShadow(
            color: colors.subtextColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: CustomButton(
        buttonText: selectedUser != null
            // ? 'Book Appointment for ${selectedUser!.firstName}'
            ? 'Book Appointment'
            : 'Select Patient to Book',
        width: double.infinity,
        height: 50.h,
        buttonState:
            selectedUser != null ? ButtonState.normal : ButtonState.disabled,
        onTap: _handleBookNow,
      ),
    );
  }

  void _showAddPatientBottomSheet(BuildContext context, bool isLoading) {
    BottomSheetHelper.instance.showAddNewPatientBottomSheet(
      context: context,
      isLoading: isLoading,
      isDismissible: !isLoading,
      enableDrag: !isLoading,
      ageController: ageController,
      nameController: patientNameController,
      onPatientAdd: () => context.userBloc.add(RegisterNewPatientEvent(
        fullName: patientNameController.text.trim(),
        mobileNumber: context.userBloc.currentUser?.phoneNumber ?? '',
        age: double.parse(ageController.text.trim()),
        ageType: 'Year',
        district: selectedDistrict?.name ?? '',
        gender: selectedGender?.genderType ?? '',
      )),
      onDistrictSelected: (value) => setState(() => selectedDistrict = value),
      onGenderSelected: (value) => setState(() => selectedGender = value),
      selectedDistrict: selectedDistrict,
      selectedGender: selectedGender,
    );
  }

  void _selectPatient(UsersByPhoneEntity user) {
    setState(() {
      selectedUser = user;
      _pIDController.text = user.pID;
      _nameController.text = user.firstName;
    });
  }

  void _handleBookNow() {
    if (selectedUser == null) {
      SnackBarHelper.showErrorSnackBar(
        context,
        message: 'Please select a patient before booking an appointment.',
      );
      return;
    }

    if (appointmentFormKey.currentState!.validate()) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PatientAppointmentConfirmPage(
            doctor: widget.doctor,
            user: selectedUser!,
          ),
        ),
      );
    }
  }
}
