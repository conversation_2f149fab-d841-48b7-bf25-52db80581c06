// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/animation_direction.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/doctor_entity.dart';
import 'package:hodan_hospital/features/doctor/presentation/pages/patient_appointnent_page.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_item_wrapper.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_app_bar.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_button.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/doctor_information_widget.dart';

class DoctorDetailsPage extends StatelessWidget {
  const DoctorDetailsPage({
    super.key,
    required this.doctor,
  });
  final DoctorEntity doctor;

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 5.h),
          //! header
          const CustomAppBar(title: 'Doctor Deatils'),

          //!!!!!!1

          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  //
                  SizedBox(height: 10.h),
                  DoctorInformationWidget(widget: doctor),

                  SizedBox(height: 30.h),
                  Padding(
                    padding: EdgeInsets.only(left: 20.w),
                    child: AnimatedItemWrapper(
                      delay: const Duration(milliseconds: 600),
                      animationDirection: AnimationDirection.topToBottom,
                      child: Text(
                        'About ${doctor.name}',
                        style: textTheme.titleLarge,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  ),
                  SizedBox(height: 10.h),
                  Padding(
                    padding: EdgeInsets.only(left: 20.w),
                    child: AnimatedItemWrapper(
                      delay: const Duration(milliseconds: 800),
                      animationDirection: AnimationDirection.topToBottom,
                      child: Text(
                        doctor.formattedServices,
                        style: textTheme.bodyMedium?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 25.h),
                  AnimatedItemWrapper(
                    delay: const Duration(milliseconds: 1000),
                    animationDirection: AnimationDirection.topToBottom,
                    child: Padding(
                      padding: EdgeInsets.only(left: 15.w),
                      child: Text(
                        'Schedule',
                        style: textTheme.titleMedium?.copyWith(
                          fontSize: 20.sp,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 5.h),
                  Padding(
                    padding: EdgeInsets.only(left: 15.w),
                    child: Row(
                      children: [
                        AnimatedItemWrapper(
                          delay: const Duration(milliseconds: 1000),
                          animationDirection: AnimationDirection.leftToRight,
                          child: Icon(
                            Icons.schedule,
                            color: context.appColors.primaryColor,
                          ),
                        ),
                        SizedBox(width: 10.w),
                        Expanded(
                          child: AnimatedItemWrapper(
                            delay: const Duration(milliseconds: 1000),
                            animationDirection: AnimationDirection.rightToLeft,
                            child: Text(
                              doctor.formattedAvailableTime,
                              style: textTheme.bodyLarge,
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                  SizedBox(height: 50.h),
                  Center(
                    child: AnimatedItemWrapper(
                      delay: const Duration(milliseconds: 1000),
                      animationDirection: AnimationDirection.topToBottom,
                      child: CustomButton(
                        width: context.screenWidth * 0.6,
                        height: 40.h,
                        buttonText: 'Book Appointment',
                        onTap: () {
                          context.pushRoute(PatientAppointnentPage(doctor: doctor));
                        },
                      ),
                    ),
                  ),
                  SizedBox(height: 20.h),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
