import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/loading_type.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_app_bar.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_list_grid_view.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_textfield.dart';
import 'package:hodan_hospital/features/doctor/presentation/bloc/doctor_bloc.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/feature_doctor_widget.dart';

import 'doctor_details_page.dart';

class DoctorsPage extends StatefulWidget {
  final String? department;
  const DoctorsPage({super.key, this.department});

  @override
  State<DoctorsPage> createState() => _DoctorsPageState();
}

class _DoctorsPageState extends State<DoctorsPage> {
  final _searchController = TextEditingController();
  bool _isSearching = false; // Track search mode

  @override
  void initState() {
    super.initState();
    if (context.doctorBloc.doctors.isEmpty) {
      context.doctorBloc.add(const GetDoctors());
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final isReturning = ModalRoute.of(context)?.isCurrent ?? false;
    if (isReturning) {
      _exitSearchMode(); // Reset search when returning
    }
  }

  /// Function to enable search mode
  void _startSearch() {
    setState(() {
      _isSearching = true;
    });
  }

  /// Function to disable search mode and reset search
  void _exitSearchMode() {
    setState(() {
      _isSearching = false;
      _searchController.clear();
      context.doctorBloc.add(const SearchDoctor(query: ''));
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AnimatedAppBar(
        backgroundColor: context.appColors.transparentColor,
        appBarHeight: 60.h,
        leadingIconColor: context.appColors.textColor,
        titleWidget: _isSearching
            ? CustomTextField(
                controller: _searchController,
                hintText: 'Search by Name or Department',
                prefixIcon: const Icon(Icons.search),
                enabledBorderColor: context.appColors.primaryColor,
                focusedBorderColor: context.appColors.primaryColor,
                autofocus: true,
                onChanged: (value) {
                  // if (value.trim().isNotEmpty) {
                  context.doctorBloc.add(SearchDoctor(query: value));
                  // }
                },
              )
            : Text(
                'Doctors  ',
                style: context.textTheme.titleLarge,
              ), // Default title when not searching
        actions: [
          IconButton(
            icon: Icon(
              _isSearching ? Icons.close : Icons.search,
              semanticLabel: _isSearching ? 'Close' : 'Search',
              color: _isSearching
                  ? context.appColors.errorColor
                  : context.appColors.textColor,
              size: 35.w,
            ),
            onPressed: () {
              if (_isSearching) {
                _exitSearchMode();
              } else {
                _startSearch();
              }
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          context.doctorBloc.add(const GetDoctors(forceFetch: true));
        },
        child: BlocBuilder<DoctorBloc, DoctorState>(
          buildWhen: (previousState, currentState) {
            return currentState is DoctorFilterLoading ||
                currentState is DoctorFiltered ||
                currentState is DoctorLoaded ||
                currentState is DoctorLoading ||
                currentState is DoctorFailure;
          },
          builder: (context, state) {
            final filteredDoctors = context.doctorBloc.filteredDoctors;
            // final isDepartmentAvailable =
            //     widget.department != null && widget.department!.isNotEmpty;
            // final filteredDoctors = isDepartmentAvailable
            //     ? doctors.where(
            //         (doctor) {
            //           final loweredDepartment =
            //               widget.department?.toLowerCase();
            //           print(
            //               "passed : $loweredDepartment current : ${doctor.department}");
            //           return doctor.department.toLowerCase() ==
            //               loweredDepartment;
            //         },
            //       ).toList()
            //     : doctors;

            // print("isDepartmentAvailable : $isDepartmentAvailable");
            // print("data length is : ${filteredDoctors.length}");
            return CustomListGridView(
              // items: context.doctorBloc.filteredDoctors,
              items: filteredDoctors,
              isLoading: state is DoctorLoading,
              isEmpty: state is DoctorFiltered && state.doctors.isEmpty ||
                  state is DoctorFailure,
              contentType: LoadingType.listView,
              // physics: AlwaysScrollableScrollPhysics(),
              itemBuilder: (context, doctor) {
                return FeatureDoctorWidget(
                  doctor: doctor,
                  onTap: () {
                    context.pushRoute(DoctorDetailsPage(doctor: doctor));
                  },
                );
              },
              onRefresh: () {
                context.doctorBloc.add(const GetDoctors());
              },
            );
          },
        ),
      ),
    );
  }
}

//!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!1
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:hodan_hospital/core/enums/loading_type.dart';
// import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
// import 'package:hodan_hospital/features/doctor/presentation/bloc/doctor_bloc.dart';
// import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_app_bar.dart';
// import 'package:hodan_hospital/features/shared/presentation/widgets/feature_doctor_widget.dart';
// import 'package:hodan_hospital/features/shared/presentation/widgets/custom_list_grid_view.dart';
// import 'package:hodan_hospital/features/doctor/domain/entities/recent_search_entity.dart';
// import 'doctor_details_page.dart';

// class DoctorsPage extends StatelessWidget {
//   final double topPadding;
//   const DoctorsPage({super.key, this.topPadding = 40});

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AnimatedAppBar(
//         title: "Doctors",
//         // actionsIconTheme: IconThemeData(
//         //   color: Colors.white,
//         // ),
//         actions: [
//           IconButton(
//             icon: Icon(Icons.search),
//             onPressed: () {
//               showSearch(
//                 context: context,
//                 delegate: DoctorSearchDelegate(
//                   doctorBloc: context.read<DoctorBloc>(),
//                 ),
//               );
//             },
//           ),
//         ],
//       ),
//       body: BlocBuilder<DoctorBloc, DoctorState>(
//         builder: (context, state) {
//           return CustomListGridView(
//             // items: state is DoctorFiltered ? state.doctors : [],
//             items: context.doctorBloc.filteredDoctors,
//             isLoading: state is DoctorLoading,
//             isEmpty: state is DoctorFiltered && state.doctors.isEmpty,
//             contentType: LoadingType.listView,
//             itemBuilder: (context, doctor) {
//               return FeatureDoctorWidget(
//                 doctor: doctor,
//                 onTap: () {
// context.pushRoute(DoctorDetailsPage(doctor : doctor));
//                 },
//               );
//             },
//             // onRefresh: () {
//             //   context.read<DoctorBloc>().add(GetDoctors(forceFetch: false));
//             // },
//             onRefresh: null,
//           );
//         },
//       ),
//     );
//   }
// }

// // 🔍 SearchDelegate Implementation with Suggestions & Recent Searches

// class DoctorSearchDelegate extends SearchDelegate {
//   final DoctorBloc doctorBloc;

//   DoctorSearchDelegate({required this.doctorBloc});

//   @override
//   String get searchFieldLabel => "Search by Name or Department";

//   /// 🔹 **Clear search button**
//   @override
//   List<Widget>? buildActions(BuildContext context) {
//     return [
//       if (query.isNotEmpty)
//         IconButton(
//           icon: Icon(Icons.clear),
//           onPressed: () {
//             query = ''; // Clear search input
//             doctorBloc
//                 .add(GetDoctors(forceFetch: false)); // Reset to all doctors
//           },
//         ),
//     ];
//   }

//   /// 🔹 **Back button**
//   @override
//   Widget? buildLeading(BuildContext context) {
//     return IconButton(
//       icon: Icon(Icons.arrow_back),
//       onPressed: () => close(context, null), // Close search
//     );
//   }

//   /// 🔹 **Show search results**
//   @override
//   Widget buildResults(BuildContext context) {
//     if (query.isEmpty) {
//       doctorBloc.add(
//           GetDoctors(forceFetch: false)); // Show all doctors if query is empty
//     } else {
//       doctorBloc.add(
//           SearchDoctor(query: query)); // Filter doctors if query is entered
//     }

//     return BlocBuilder<DoctorBloc, DoctorState>(
//       builder: (context, state) {
//         return CustomListGridView(
//           items: context.doctorBloc.filteredDoctors,
//           isLoading: state is DoctorLoading,
//           isEmpty: state is DoctorFiltered && state.doctors.isEmpty,
//           contentType: LoadingType.listView,
//           itemBuilder: (context, doctor) {
//             return FeatureDoctorWidget(
//               doctor: doctor,
//               onTap: () {
//                 close(context, null);
//                 Navigator.push(
//                   context,
//                   MaterialPageRoute(
//                     builder: (context) => DoctorDetailsPage(doctor: doctor),
//                   ),
//                 );
//               },
//             );
//           },
//           onRefresh: () => doctorBloc.add(GetDoctors(forceFetch: false)),
//         );
//       },
//     );
//   }

//   /// 🔹 **Show suggestions & recent searches**
//   @override
//   Widget buildSuggestions(BuildContext context) {
//     if (query.isEmpty) {
//       doctorBloc.add(GetRecentSearch()); // Load recent searches
//       doctorBloc
//           .add(GetDoctors(forceFetch: false)); // Show all doctors initially
//     } else {
//       doctorBloc.add(SearchDoctor(query: query)); // Filter doctors when typing
//     }

//     return BlocBuilder<DoctorBloc, DoctorState>(
//       builder: (context, state) {
//         if (query.isEmpty) {
//           return Column(
//             children: [
//               BlocBuilder<DoctorBloc, DoctorState>(
//                 builder: (context, state) {
//                   if (
//                       // state is RecentSearchesLoaded &&
//                       context.doctorBloc.recentSearches.isNotEmpty) {
//                     return Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Padding(
//                           padding: EdgeInsets.all(10),
//                           child: Text(
//                             "Recent Searches",
//                             style: Theme.of(context).textTheme.bodyLarge,
//                           ),
//                         ),
//                         ListView.builder(
//                           shrinkWrap: true,
//                           physics: NeverScrollableScrollPhysics(),
//                           itemCount: context.doctorBloc.recentSearches.length,
//                           itemBuilder: (context, index) {
//                             final search =
//                                 context.doctorBloc.recentSearches[index];
//                             return ListTile(
//                               title: Text(search.query),
//                               leading: Icon(Icons.history),
//                               onTap: () {
//                                 query = search.query;
//                                 showResults(context);
//                               },
//                             );
//                           },
//                         ),
//                       ],
//                     );
//                   }
//                   return SizedBox(); // Empty if no recent searches
//                 },
//               ),
//               Expanded(
//                 child: CustomListGridView(
//                   items: state is DoctorLoaded ? state.doctors : [],
//                   isLoading: state is DoctorLoading,
//                   isEmpty: state is DoctorLoaded && state.doctors.isEmpty,
//                   contentType: LoadingType.listView,
//                   itemBuilder: (context, doctor) {
//                     return FeatureDoctorWidget(
//                       doctor: doctor,
//                       onTap: () {
//                         close(context, null);
//                         Navigator.push(
//                           context,
//                           MaterialPageRoute(
//                             builder: (context) =>
//                                 DoctorDetailsPage(doctor: doctor),
//                           ),
//                         );
//                       },
//                     );
//                   },
//                   onRefresh: () =>
//                       doctorBloc.add(GetDoctors(forceFetch: false)),
//                 ),
//               ),
//             ],
//           );
//         } else {
//           return CustomListGridView(
//             items: state is DoctorFiltered ? state.doctors : [],
//             isLoading: state is DoctorLoading,
//             isEmpty: state is DoctorFiltered && state.doctors.isEmpty,
//             contentType: LoadingType.listView,
//             itemBuilder: (context, doctor) {
//               return ListTile(
//                 title: Text(doctor.name),
//                 subtitle: Text(doctor.department),
//                 leading: Icon(Icons.person),
//                 onTap: () {
//                   query = doctor.name;
//                   doctorBloc.add(SaveRecentSearch(
//                     recentSearchEntity: RecentSearchEntity(query: doctor.name),
//                   ));
//                   showResults(context);
//                 },
//               );
//             },
//             onRefresh: null,
//           );
//         }
//       },
//     );
//   }
// }
