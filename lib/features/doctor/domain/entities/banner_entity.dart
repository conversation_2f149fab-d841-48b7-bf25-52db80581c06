import 'package:hodan_hospital/core/utils/extensions/app_date_extensions.dart';

import '../../../../core/enums/date_format_type_enum.dart';

class BannerEntity {
  final String name;
  final String imageUrl;
  final String bannerType;
  final String title;
  final String? description;
  final String? details;
  final DateTime? validFrom;
  final DateTime? validTill;

  BannerEntity({
    required this.name,
    required this.imageUrl,
    required this.bannerType,
    required this.title,
    this.description,
    this.details,
    this.validFrom,
    this.validTill,
  });

  // bool get isValid {
  //   if (validFrom != null && validTill != null) {
  //     return DateTime.now().isAfter(validFrom!) &&
  //         DateTime.now().isBefore(validTill!);
  //   }
  //   return false;
  // }

  // bool get isExpired {
  //   if (validTill != null) {
  //     return DateTime.now().isAfter(validTill!);
  //   }
  //   return false;
  // }

  // bool get isValidOrNotExpired {
  //   return isValid || !isExpired;
  // }

  String get formattedValidFrom {
    return validFrom?.toFormattedString(
          formatType: DateFormatType.dayShortMonthYear,
        ) ??
        '';
  }

  String get formattedValidTill {
    return validTill?.toFormattedString(
          formatType: DateFormatType.dayShortMonthYear,
        ) ??
        '';
  }

  @override
  String toString() =>
      'BannerEntity(name: $name, imageUrl: $imageUrl, bannerType: $bannerType, title: $title, description: $description, details: $details, validFrom: $validFrom, validTill: $validTill, formattedValidFrom: $formattedValidFrom, formattedValidTill: $formattedValidTill)';
}
