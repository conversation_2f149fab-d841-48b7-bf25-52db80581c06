import 'package:hodan_hospital/core/enums/parse_failure_type.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
// import 'package:objectbox/objectbox.dart';

// @Entity()
class BannerModel {
  // @Id()
  int id = 0;

  final String name;
  final String imageUrl;
  final String bannerType;
  final String title;
  final String? description;
  final String? details;
  final DateTime? validFrom;
  final DateTime? validTill;

  BannerModel({
    required this.name,
    required this.imageUrl,
    required this.bannerType,
    required this.title,
    this.description,
    this.details,
    this.validFrom,
    this.validTill,
  });

  factory BannerModel.fromJson(Map<String, dynamic> json) {
    try {
      return BannerModel(
        name: json['name'] ?? '',
        imageUrl: json['banner_image'] ?? '',
        bannerType: json['banner_type'] ?? '',
        title: json['title'] ?? '',
        description: json['description'],
        details: json['details'],
        validFrom: json['valid_from'] != null
            ? DateTime.tryParse(json['valid_from'])
            : null,
        validTill: json['valid_till'] != null
            ? DateTime.tryParse(json['valid_till'])
            : null,
      );
    } catch (error, stackTrace) {
      throw ParsingFailure(
        message: 'Failed to parse BannerModel: ${error.toString()}',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: BannerModel,
        stackTrace: stackTrace,
      );
    }
  }

  static List<BannerModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList
        .map((json) => BannerModel.fromJson(json as Map<String, dynamic>))
        .toList();
  }
}
