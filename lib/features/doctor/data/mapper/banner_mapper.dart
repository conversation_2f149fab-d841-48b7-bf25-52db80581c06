import 'package:hodan_hospital/features/doctor/data/models/banner_model.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/banner_entity.dart';

class BannerMapper {
  /// Converts a BannerEntity to a BannerModel
  static BannerModel entityToModel(BannerEntity entity) {
    return BannerModel(
      name: entity.name,
      imageUrl: entity.imageUrl,
      bannerType: entity.bannerType,
      title: entity.title,
      description: entity.description,
      details: entity.details,
      validFrom: entity.validFrom,
      validTill: entity.validTill,
    );
  }

  /// Converts a BannerModel to a BannerEntity
  static BannerEntity modelToEntity(BannerModel model) {
    return BannerEntity(
      name: model.name,
      imageUrl: model.imageUrl,
      bannerType: model.bannerType,
      title: model.title,
      description: model.description,
      details: model.details,
      validFrom: model.validFrom,
      validTill: model.validTill,
    );
  }

  /// Converts a list of BannerModel to a list of BannerEntity
  static List<BannerEntity> modelListToEntityList(List<BannerModel> models) {
    return models.map((model) => modelToEntity(model)).toList();
  }

  /// Converts a list of BannerEntity to a list of BannerModel
  static List<BannerModel> entityListToModelList(List<BannerEntity> entities) {
    return entities.map((entity) => entityToModel(entity)).toList();
  }
}
