// ignore_for_file: unnecessary_null_comparison

import 'package:dio/dio.dart';
import 'package:fpdart/fpdart.dart';
import 'package:hodan_hospital/core/config/enviroment/enviroment_config.dart';
import 'package:hodan_hospital/core/constants/api_end_points.dart';
import 'package:hodan_hospital/core/enums/http_method.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/errors/http_error_handler.dart';
import 'package:hodan_hospital/core/network/api_client/dio_api_client.dart';
import 'package:hodan_hospital/core/utils/helpers/request_data.dart';
import 'package:hodan_hospital/core/utils/helpers/response_handler.dart';
import 'package:hodan_hospital/features/Order/data/models/order_model.dart';

import '../../../../../core/services/payment_service.dart';

abstract class OrderRemoteDataSource {
  FutureEitherFailOr<List<OrderModel>> getOrders({
    required String mobileNo,
  });
  FutureEitherFailOr<String> processOrder({
    required String orderId,
    required String mobileNo,
    required double amount,
  });
}

class OrderRemoteDataSourceImpl implements OrderRemoteDataSource {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;
  final PaymentService paymentService;

  OrderRemoteDataSourceImpl({
    required this.dioApiClient,
    required this.httpErrorHandler,
    required this.paymentService,
  });

  /// Fetches all orders for a given mobile number
  @override
  FutureEitherFailOr<List<OrderModel>> getOrders({
    required String mobileNo,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (data) => OrderModel.fromJsonList(data as List<dynamic>),
      requestFunction: () => dioApiClient.request(
          method: HttpMethod.get,
          endPointUrl: ApiEndpoints.getOrders,
          data: RequestData.json({
            'mobile': mobileNo,
          })),
    );

    return ResponseHandler<List<OrderModel>>(response)
        .handleResponseAndExtractData(
      onFailure: (failure) => left(failure),
      onSuccess: (data) => right(data),
    );
  }

  // @override
  // FutureEitherFailOr<String> processOrder({
  //   required String orderId,
  //   required String mobileNo,
  //   required double amount,
  // }) async {
  //   final cancelToken = CancelToken();

  //   // final url = EnvironmentConfig.hodanMerchantApiUrl;

  //   // final payload = _createPayload(
  //   //   serviceName: 'API_PURCHASE',
  //   //   patientMobile: mobileNo,
  //   //   // amount: amount,
  //   //   amount: 0.1, // for testing
  //   // );

  //   // final paymentResult = await httpErrorHandler.handlePaymentRequest(
  //   //   requestFunction: () => dioApiClient.request(
  //   //     method: HttpMethod.post,
  //   //     endPointUrl: url,
  //   //     data: RequestData.json(payload),
  //   //     receiveTimeout: const Duration(seconds: 50),
  //   //     cancelToken: cancelToken,
  //   //   ),
  //   // );

  //   final paymentResult = await paymentService.processPayment(
  //     amount: amount,
  //     patientMobile: mobileNo,
  //     context: 'Order',
  //     contextData: {
  //       'orderId': orderId,
  //     },
  //     shouldReverseAfterSuccess: false,
  //     cancelToken: cancelToken,
  //   );

  //   return paymentResult.fold(
  //     (failure) async {
  //       //
  //       cancelToken.cancel(
  //           'Request order saving cancel due to payment failure'); // Cancel on failure

  //       return left(failure);
  //     },
  //     (transactionId) async {
  //       // String? transactionId;

  //       // Extract orderId from successful payment
  //       // if (success['params'] is Map) {
  //       //   transactionId = success['params']['transactionId'].toString();
  //       // }

  //       final orderResult = await _saveOrder(
  //         orderId: orderId,
  //       );

  //       return orderResult.fold(
  //         (orderFailure) async {
  //           // 3. Reverse payment if appointment fails
  //           if (transactionId != 'unknown') {
  //             await _handlePaymentReversal(
  //               transactionId: transactionId,
  //               patientMobile: mobileNo,
  //               amount: amount,
  //             );
  //           }
  //           return left(orderFailure);
  //         },
  //         (success) async {
  //           // 3. Reverse payment if appointment fails
  //           if (transactionId != 'unknown') {
  //             await _handlePaymentReversal(
  //               transactionId: transactionId,
  //               patientMobile: mobileNo,
  //               amount: amount,
  //             );
  //           }
  //           return right(success);
  //         },
  //       );
  //     },
  //   );
  // }

  /// Processes an order with payment and saves it to the system
  /// Automatically handles refunds if order saving fails
  @override
  FutureEitherFailOr<String> processOrder({
    required String orderId,
    required String mobileNo,
    required double amount,
  }) async {
    final cancelToken = CancelToken();

    // Process payment
    final paymentResult = await paymentService.processPayment(
      // amount: amount, for production
      amount: 0.01, // for testing
      patientMobile: mobileNo,
      context: 'Order',
      contextData: {'orderId': orderId},
      // isTesting: true,
      cancelToken: cancelToken,
    );

    return await paymentResult.fold(
      (paymentFailure) async {
        return left(paymentFailure);
      },
      (transactionId) async {
        // Save order after successful payment
        final orderResult = await _saveOrder(
          orderId: orderId,
          cancelToken: cancelToken,
        );

        return orderResult.fold(
          (orderFailure) async {
            // Automatic reversal on order save failure
            if (transactionId.trim().isNotEmpty && transactionId != 'unknown') {
              await paymentService.processRefund(
                transactionId: transactionId,
                patientMobile: mobileNo,
                amount: amount,
                cancelToken: cancelToken,
              );
            }
            cancelToken.cancel(
                'Request order saving cancel due to order save failure');
            return left(orderFailure);
          },
          (success) => right(success),
        );
      },
    );
  }

  FutureEitherFailOr<String> _saveOrder({
    required String orderId,
    CancelToken? cancelToken,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndpoints.saveOrder,
        data: RequestData.json(
          {
            'sales_order_id': orderId,
          },
        ),
        cancelToken: cancelToken,
      ),
    );

    return response.fold(
      (failure) {
        //
        return left(failure);
      },
      (apiResponse) {
        //
        return right(apiResponse.apiMessage);
      },
    );
  }

  // FutureEitherFailOr<String> _handlePaymentReversal({
  //   required String transactionId,
  //   required String patientMobile,
  //   required double amount,
  // }) async {
  //   return paymentService.processRefund(
  //     transactionId: transactionId,
  //     patientMobile: patientMobile,
  //     amount: amount,
  //   );
  // }

  /// Create a payload for the payment request
  // Map<String, Object> _createPayload({
  //   required String serviceName,
  //   required String patientMobile,
  //   required double amount,
  //   String? transactionId, // Optional transactionId: Used for reversal
  // }) {
  //   final serviceParams = {
  //     'merchantUid': EnvironmentConfig.hodanMerchantUid,
  //     // "merchantUid": EnvironmentConfig.rasiinMerchantUid,
  //     'apiUserId': EnvironmentConfig.hodanMerchantApiUserId,
  //     // "apiUserId": EnvironmentConfig.rasiinMerchantApiUserId,
  //     'apiKey': EnvironmentConfig.hodanMerchantApiKey,
  //     // "apiKey": EnvironmentConfig.rasiinMerchantApiKey,
  //     'paymentMethod': 'MWALLET_ACCOUNT',
  //     'payerInfo': {'accountNo': patientMobile},
  //   };

  //   // Add transaction-specific parameters based on service type
  //   if (serviceName == 'API_PURCHASE') {
  //     serviceParams['transactionInfo'] = {
  //       'referenceId': '********',
  //       'invoiceId': '********',
  //       'amount': amount,
  //       'currency': 'USD',
  //       'description': 'Medical Appointment Payment'
  //     };
  //   } else {
  //     // For API_CANCELPURCHASE
  //     serviceParams.addAll({
  //       'transactionId': transactionId ?? '',
  //       'referenceId': 'REF-${DateTime.now().millisecondsSinceEpoch}',
  //       'description': 'Reversal due to appointment saving failure'
  //     });
  //   }

  //   return {
  //     'schemaVersion': '1.0',
  //     'requestId': '***********',
  //     'timestamp': '2024-06-2 Africa',
  //     'channelName': 'WEB',
  //     'serviceName': serviceName,
  //     'serviceParams': serviceParams,
  //   };
  // }
}
