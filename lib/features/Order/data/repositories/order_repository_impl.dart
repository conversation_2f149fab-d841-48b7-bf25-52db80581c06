import 'package:fpdart/fpdart.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/features/Order/data/datasources/remote/order_remote_data_source.dart';
import 'package:hodan_hospital/features/Order/data/mapper/order_mapper.dart';
import 'package:hodan_hospital/features/Order/data/models/order_model.dart';
import 'package:hodan_hospital/features/Order/domain/entities/order_entity.dart';
import 'package:hodan_hospital/features/Order/domain/repositories/order_repository.dart';

class OrderRepositoryImpl implements OrderRepository {
  final OrderRemoteDataSource remoteDataSource;
  // final OrderLocalDataSource localDataSource;

  OrderRepositoryImpl({
    required this.remoteDataSource,
    // required this.localDataSource,
  });

  @override
  FutureEitherFailOr<List<OrderEntity>> getAllOrders({
    required bool forceFetch,
    required String mobileNumber,
  }) async {
    try {
      // if (!forceFetch) {
      //   final cachedResponse = await _getCachedOrders();
      //   if (cachedResponse.isRight()) {
      //     return cachedResponse;
      //   }
      // }

      final remoteResponse =
          await _fetchOrdersFromServer(mobileNo: mobileNumber);
      return remoteResponse.fold(
        (failure) async {
          // final cachedResponse = await _getCachedOrders();
          // if (cachedResponse.isRight()) {
          //   return cachedResponse;
          // }
          return left(failure);
        },
        (orders) async {
          // await localDataSource.saveOrders(orders: orders);
          final orderEntities = OrderMapper.modelListToEntityList(orders);
          return right(orderEntities);
        },
      );
    } catch (error, stackTrace) {
      AppLogger().error(
        'Unexpected error in getAppointments',
        error: error,
        stackTrace: stackTrace,
      );
      return left(UnexpectedFailure(
        message: 'Unexpected error in getAppointments',
        stackTrace: stackTrace,
      ));
    }
  }

  // FutureEitherFailOr<List<OrderEntity>> _getCachedOrders() async {
  //   final cachedResponse = await localDataSource.readOrders();
  //   return cachedResponse.map(
  //     (appointmentModels) {
  //       final appointmentEntities =
  //           OrderMapper.modelListToEntityList(appointmentModels);
  //       return appointmentEntities;
  //     },
  //   );
  // }

  FutureEitherFailOr<List<OrderModel>> _fetchOrdersFromServer({
    required String mobileNo,
  }) async {
    return await remoteDataSource.getOrders(mobileNo: mobileNo);
  }

  @override
  FutureEitherFailOr<String> processOrder({
    required String orderId,
    required String mobileNo,
    required double amount,
  }) async {
    final response = await remoteDataSource.processOrder(
      orderId: orderId,
      mobileNo: mobileNo,
      amount: amount,
    );
    return response;
  }
}
