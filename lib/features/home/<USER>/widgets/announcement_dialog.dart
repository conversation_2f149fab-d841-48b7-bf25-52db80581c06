import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/image_place_holder.dart';

import '../../../doctor/domain/entities/banner_entity.dart';

/*


| Property              | Value                         |
| --------------------- | ----------------------------- |
| **Aspect Ratio**      | `16:9` (standard & versatile) |
| **Recommended Size**  | `1280 × 720 px`               |
| **Minimum Safe Size** | `960 × 540 px`                |
| **File Format**       | `.jpg` or `.png`              |
| **Optimization**      | Under 300 KB (web-safe size)  |



*/

void showAnnouncementBannerDialog({
  required BuildContext context,
  required List<BannerEntity> announcementBanners,
}) {
  if (announcementBanners.isEmpty) return;
  final imageUrl = announcementBanners.first.imageUrl;
  final details = announcementBanners.first.details ?? '';

  showDialog(
    context: context,
    // barrierDismissible: true,
    builder: (context) {
      final colors = context.appColors;
      final maxHeight = MediaQuery.of(context).size.height * 0.75;

      return Dialog(
        backgroundColor: colors.cardColor,
        insetPadding: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxHeight: maxHeight,
                ),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(height: 30),

                      // Banner Image
                      ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: CachedNetworkImage(
                          imageUrl: imageUrl,
                          width: double.infinity,
                          height: 180,
                          fit: BoxFit.cover,
                          placeholder: (context, url) =>
                              const ImagePlaceholder(),
                          errorWidget: (context, url, error) =>
                              const ImagePlaceholder(),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Details Text
                      Text(
                        details,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                          height: 1.6,
                          fontWeight: FontWeight.w500,
                          color: colors.textColor,
                        ),
                      ),

                      const SizedBox(height: 16),
                    ],
                  ),
                ),
              ),
            ),

            // Close Button
            Positioned(
              top: 4,
              left: 12,
              child: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: CircleAvatar(
                  radius: 16,
                  backgroundColor: colors.secondaryColor.withValues(alpha: 0.9),
                  child: Icon(
                    Icons.close,
                    color: colors.whiteColor,
                    size: 20,
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    },
  );
}
