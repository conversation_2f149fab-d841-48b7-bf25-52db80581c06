// ignore_for_file: use_build_context_synchronously

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/core/utils/helpers/snack_bar_helper.dart';
import 'package:hodan_hospital/features/appointment/presentation/bloc/appointment_bloc.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_app_bar.dart';
import 'package:hodan_hospital/core/services/scanner_permission_service.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

class ScanAppointmentPage extends StatefulWidget {
  const ScanAppointmentPage({super.key});

  @override
  State<ScanAppointmentPage> createState() => _ScanAppointmentPageState();
}

class _ScanAppointmentPageState extends State<ScanAppointmentPage> {
  final double scanSize = 300.0;
  double _scanLinePosition = 0.0;
  Timer? _scanLineTimer;
  final ScannerPermissionService _scannerPermissionService =
      ScannerPermissionService();

  @override
  void initState() {
    super.initState();
    _checkPermission();
    _startScanLineAnimation();
  }

  void _checkPermission() {
    Future.delayed(Duration.zero, () {
      context
          .read<AppointmentBloc>()
          .add(CheckScannerPermissionEvent(context: context));
    });
  }

  void _startScanLineAnimation() {
    _scanLineTimer?.cancel();
    _scanLineTimer = Timer.periodic(const Duration(milliseconds: 50), (timer) {
      if (mounted) {
        setState(() {
          _scanLinePosition += 2;
          if (_scanLinePosition > scanSize) {
            _scanLinePosition = 0;
          }
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    final appointmentBloc = context.appointmentBloc;

    return BlocConsumer<AppointmentBloc, AppointmentState>(
      listener: (context, state) {
        AppLogger().info('State is : $state');

        if (state is CodeScannedSuccess) {
          HapticFeedback.mediumImpact();
          Navigator.pop(context, state.code);
        }

        if (state is ScannerError) {
          context.popRoute();
          SnackBarHelper.showErrorSnackBar(
            context,
            message: state.error,
          );
        }

        if (state is ScannerPermissionGranted) {
          context.read<AppointmentBloc>().add(const ToggleScannerFlashEvent());
        }
      },
      builder: (context, state) {
        if (state is ScannerPermissionDenied) {
          return _buildPermissionDeniedUI(context);
        }
        return Scaffold(
          appBar: AnimatedAppBar(
            title: 'Scan Appointment',
            leading: IconButton(
              icon: Icon(Icons.arrow_back, color: appColors.whiteColor),
              onPressed: () => context.popRoute(),
            ),
            actions: [
              IconButton(
                icon: Icon(
                  context.appointmentBloc.isFlashOn
                      ? Icons.flash_on
                      : Icons.flash_off,
                  color: appColors.whiteColor,
                ),
                onPressed: () {
                  appointmentBloc.add(const ToggleScannerFlashEvent());
                },
              ),
              IconButton(
                icon: Icon(
                  Icons.cameraswitch,
                  color: appColors.whiteColor,
                ),
                onPressed: () {
                  appointmentBloc.add(const SwitchScannerCameraEvent());
                },
              ),
            ],
          ),
          body: Stack(
            alignment: Alignment.center,
            children: [
              MobileScanner(
                controller: context.appointmentBloc.scannerController,
                scanWindow: Rect.fromCenter(
                  center: Offset(
                    MediaQuery.of(context).size.width / 2,
                    MediaQuery.of(context).size.height / 2,
                  ),
                  width: scanSize,
                  height: scanSize,
                ),
                onDetect: (capture) {
                  for (final barcode in capture.barcodes) {
                    AppLogger().info('Barcode is : ${barcode.rawValue}');
                    appointmentBloc.add(
                      CodeScannedEvent(
                        code: barcode.rawValue ?? '',
                      ),
                    );
                  }
                },
              ),
              Container(
                width: scanSize,
                height: scanSize,
                decoration: BoxDecoration(
                  border: Border.all(color: appColors.whiteColor, width: 2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Stack(
                  children: [
                    Positioned(
                      top: _scanLinePosition * (scanSize / scanSize),
                      child: Container(
                        width: scanSize,
                        height: 2,
                        color: appColors.primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
              Positioned(
                bottom: 60,
                child: Column(
                  children: [
                    Icon(Icons.qr_code_scanner,
                        color: appColors.whiteColor, size: 40),
                    const SizedBox(height: 12),
                    Text(
                      'Scan your appointment QR code',
                      style: textTheme.titleMedium?.copyWith(
                        color: appColors.whiteColor,
                        fontSize: 18,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPermissionDeniedUI(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            // Icons.camera_alt_off,
            Icons.camera_alt,
            size: 64,
            color: context.appColors.errorColor,
          ),
          const SizedBox(height: 16),
          Text(
            'Camera Permission Required',
            style: context.textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          const Text(
            'Please enable camera access to scan QR codes',
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () async {
              // Use the new scanner permission service to check and request permission
              final hasPermission = await _scannerPermissionService
                  .checkAndRequestCameraPermission(
                context: context,
              );

              if (hasPermission) {
                // If permission granted, trigger the bloc to check again
                _checkPermission();
              }
            },
            child: const Text('Grant Permission'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _scanLineTimer?.cancel();
    super.dispose();
  }
}
