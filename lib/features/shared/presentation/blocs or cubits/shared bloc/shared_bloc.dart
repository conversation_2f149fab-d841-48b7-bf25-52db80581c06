import 'package:equatable/equatable.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hodan_hospital/core/config/di/dependency_injection.dart';
import 'package:hodan_hospital/core/constants/analytics_events.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/services/analytics_service.dart';
import 'package:hodan_hospital/core/utils/helpers/bloc_helper.dart';
import 'package:hodan_hospital/features/shared/domain/params/generate_pdf_params.dart';
import 'package:hodan_hospital/features/shared/domain/params/download_pdf_params.dart';

import '../../../domain/params/send_sms_params.dart';
import '../../../domain/usecases/download_pdf_use_case.dart';
import '../../../domain/usecases/generate_pdf_use_case.dart';
import '../../../domain/usecases/send_sms_usecase.dart';

part 'shared_event.dart';
part 'shared_state.dart';

class SharedBloc extends Bloc<SharedEvent, SharedState> {
  final GeneratePdfUseCase generatePdfUseCase;
  final DownloadPdfUseCase downloadPdfUseCase;
  final SendSMSUseCase sendSMSUseCase;

  SharedBloc({
    required this.generatePdfUseCase,
    required this.downloadPdfUseCase,
    required this.sendSMSUseCase,
  }) : super(SharedInitial()) {
    on<GenerateOrderPdf>(
      _onGenerateOrderPdf,
      transformer: BlocHelper.debounceHelper(),
    );
    on<GenerateAppointmentPdf>(
      _onGenerateAppointmentPdf,
      transformer: BlocHelper.debounceHelper(),
    );
    on<GenerateLabResultPdf>(
      _onGenerateLabResultPdf,
      transformer: BlocHelper.debounceHelper(),
    );
    on<DownloadOrderPdf>(
      _onDownloadOrderPdf,
      transformer: BlocHelper.debounceHelper(),
    );
    on<DownloadAppointmentPdf>(
      _onDownloadAppointmentPdf,
      transformer: BlocHelper.debounceHelper(),
    );
    on<SendSMS>(
      _onSendSMS,
      transformer: BlocHelper.debounceHelper(),
    );
  }

  final _analyticsService = sl<AnalyticsService>();

  Future<void> _onGenerateOrderPdf(
    GenerateOrderPdf event,
    Emitter<SharedState> emit,
  ) async {
    // Track PDF generation initiated
    _analyticsService.logEvent(
      name: AnalyticsEvents.order_pdf_generated,
      parameters: {
        'document_id': event.id,
        'document_type': 'Sales Order',
        'format': 'Sales Order',
        'timestamp': DateTime.now().toIso8601String(),
        'action': 'generate',
      },
    );

    await BlocHelper.handleEventAndEmit<Uint8List, SharedState>(
      emit: emit,
      loadingState: GenerateOrderPdfLoading(),
      callUseCase: generatePdfUseCase(
        params: GeneratePdfParams(
          id: event.id,
          doctype: 'Sales Order',
          format: 'Sales Order',
        ),
      ),
      onSuccess: (pdfBytes) {
        // Track successful PDF generation
        _analyticsService.logEvent(
          name: AnalyticsEvents.order_pdf_generated,
          parameters: {
            'document_id': event.id,
            'document_type': 'Sales Order',
            'format': 'Sales Order',
            'pdf_size_bytes': pdfBytes.length.toString(),
            'timestamp': DateTime.now().toIso8601String(),
            'action': 'generate_success',
            'status': 'success',
          },
        );
        return GenerateOrderPdfSuccess(pdfBytes);
      },
      onFailure: (failure) {
        // Track PDF generation failure
        _analyticsService.logEvent(
          name: AnalyticsEvents.order_pdf_generated,
          parameters: {
            'document_id': event.id,
            'document_type': 'Sales Order',
            'format': 'Sales Order',
            'error_message': failure.getErrorMessage(),
            'error_type': failure.runtimeType.toString(),
            'timestamp': DateTime.now().toIso8601String(),
            'action': 'generate_failed',
            'status': 'failed',
          },
        );
        return GenerateOrderPdfFailure(failure);
      },
    );
  }

  Future<void> _onGenerateAppointmentPdf(
    GenerateAppointmentPdf event,
    Emitter<SharedState> emit,
  ) async {
    // Track appointment PDF generation initiated
    _analyticsService.logEvent(
      name: AnalyticsEvents.appointment_pdf_generated,
      parameters: {
        'document_id': event.id,
        'document_type': 'Que',
        'format': 'Que Printing',
        'timestamp': DateTime.now().toIso8601String(),
        'action': 'generate',
      },
    );

    await BlocHelper.handleEventAndEmit<Uint8List, SharedState>(
      emit: emit,
      loadingState: GenerateAppointmentPdfLoading(),
      callUseCase: generatePdfUseCase(
        params: GeneratePdfParams(
          id: event.id,
          doctype: 'Que',
          format: 'Que Printing',
        ),
      ),
      onSuccess: (pdfBytes) {
        // Track successful appointment PDF generation
        _analyticsService.logEvent(
          name: AnalyticsEvents.appointment_pdf_generated,
          parameters: {
            'document_id': event.id,
            'document_type': 'Que',
            'format': 'Que Printing',
            'pdf_size_bytes': pdfBytes.length.toString(),
            'timestamp': DateTime.now().toIso8601String(),
            'action': 'generate_success',
            'status': 'success',
          },
        );
        return GenerateAppointmentPdfSuccess(pdfBytes);
      },
      onFailure: (failure) {
        // Track appointment PDF generation failure
        _analyticsService.logEvent(
          name: AnalyticsEvents.appointment_pdf_generated,
          parameters: {
            'document_id': event.id,
            'document_type': 'Que',
            'format': 'Que Printing',
            'error_message': failure.getErrorMessage(),
            'error_type': failure.runtimeType.toString(),
            'timestamp': DateTime.now().toIso8601String(),
            'action': 'generate_failed',
            'status': 'failed',
          },
        );
        return GenerateAppointmentPdfFailure(failure);
      },
    );
  }

  Future<void> _onGenerateLabResultPdf(
    GenerateLabResultPdf event,
    Emitter<SharedState> emit,
  ) async {
    // Track lab result PDF generation initiated
    _analyticsService.logEvent(
      name: AnalyticsEvents.lab_result_pdf_generated,
      parameters: {
        'document_id': event.id,
        'document_type': 'Lab Result',
        'format': 'Standard',
        'timestamp': DateTime.now().toIso8601String(),
        'action': 'generate',
      },
    );

    await BlocHelper.handleEventAndEmit<Uint8List, SharedState>(
      emit: emit,
      loadingState: GenerateLabResultPdfLoading(),
      callUseCase: generatePdfUseCase(
        params: GeneratePdfParams(
          id: event.id,
          doctype: 'Lab Result',
          format: 'Standard',
        ),
      ),
      onSuccess: (pdfBytes) {
        // Track successful lab result PDF generation
        _analyticsService.logEvent(
          name: AnalyticsEvents.lab_result_pdf_generated,
          parameters: {
            'document_id': event.id,
            'document_type': 'Lab Result',
            'format': 'Standard',
            'pdf_size_bytes': pdfBytes.length.toString(),
            'timestamp': DateTime.now().toIso8601String(),
            'action': 'generate_success',
            'status': 'success',
          },
        );
        return GenerateLabResultPdfSuccess(pdfBytes);
      },
      onFailure: (failure) {
        // Track lab result PDF generation failure
        _analyticsService.logEvent(
          name: AnalyticsEvents.lab_result_pdf_generated,
          parameters: {
            'document_id': event.id,
            'document_type': 'Lab Result',
            'format': 'Standard',
            'error_message': failure.getErrorMessage(),
            'error_type': failure.runtimeType.toString(),
            'timestamp': DateTime.now().toIso8601String(),
            'action': 'generate_failed',
            'status': 'failed',
          },
        );
        return GenerateLabResultPdfFailure(failure);
      },
    );
  }

  Future<void> _onDownloadOrderPdf(
    DownloadOrderPdf event,
    Emitter<SharedState> emit,
  ) async {
    // Track order PDF download initiated
    _analyticsService.logEvent(
      name: AnalyticsEvents.order_pdf_downloaded,
      parameters: {
        'pdf_size_bytes': event.pdfBytes.length.toString(),
        'document_type': 'Sales Order',
        'timestamp': DateTime.now().toIso8601String(),
        'action': 'download',
      },
    );

    await BlocHelper.handleEventAndEmit<String, SharedState>(
      emit: emit,
      loadingState: DownloadOrderPdfLoading(),
      callUseCase: downloadPdfUseCase(
        params: DownloadPdfParams(pdfBytes: event.pdfBytes),
      ),
      onSuccess: (filePath) {
        // Track successful order PDF download
        _analyticsService.logEvent(
          name: AnalyticsEvents.order_pdf_downloaded,
          parameters: {
            'pdf_size_bytes': event.pdfBytes.length.toString(),
            'document_type': 'Sales Order',
            'file_path': filePath,
            'timestamp': DateTime.now().toIso8601String(),
            'action': 'download_success',
            'status': 'success',
          },
        );
        return DownloadOrderPdfSuccess(filePath);
      },
      onFailure: (failure) {
        // Track order PDF download failure
        _analyticsService.logEvent(
          name: AnalyticsEvents.order_pdf_downloaded,
          parameters: {
            'pdf_size_bytes': event.pdfBytes.length.toString(),
            'document_type': 'Sales Order',
            'error_message': failure.getErrorMessage(),
            'error_type': failure.runtimeType.toString(),
            'timestamp': DateTime.now().toIso8601String(),
            'action': 'download_failed',
            'status': 'failed',
          },
        );
        return DownloadOrderPdfFailure(failure);
      },
    );
  }

  Future<void> _onDownloadAppointmentPdf(
    DownloadAppointmentPdf event,
    Emitter<SharedState> emit,
  ) async {
    // Track appointment PDF download initiated
    _analyticsService.logEvent(
      name: AnalyticsEvents.appointment_pdf_downloaded,
      parameters: {
        'pdf_size_bytes': event.pdfPytes.length.toString(),
        'document_type': 'Que',
        'timestamp': DateTime.now().toIso8601String(),
        'action': 'download',
      },
    );

    await BlocHelper.handleEventAndEmit<String, SharedState>(
      emit: emit,
      loadingState: DownloadAppointmentPdfLoading(),
      callUseCase: downloadPdfUseCase(
        params: DownloadPdfParams(pdfBytes: event.pdfPytes),
      ),
      onSuccess: (filePath) {
        // Track successful appointment PDF download
        _analyticsService.logEvent(
          name: AnalyticsEvents.appointment_pdf_downloaded,
          parameters: {
            'pdf_size_bytes': event.pdfPytes.length.toString(),
            'document_type': 'Que',
            'file_path': filePath,
            'timestamp': DateTime.now().toIso8601String(),
            'action': 'download_success',
            'status': 'success',
          },
        );
        return DownloadAppointmentPdfSuccess(filePath);
      },
      onFailure: (failure) {
        // Track appointment PDF download failure
        _analyticsService.logEvent(
          name: AnalyticsEvents.appointment_pdf_downloaded,
          parameters: {
            'pdf_size_bytes': event.pdfPytes.length.toString(),
            'document_type': 'Que',
            'error_message': failure.getErrorMessage(),
            'error_type': failure.runtimeType.toString(),
            'timestamp': DateTime.now().toIso8601String(),
            'action': 'download_failed',
            'status': 'failed',
          },
        );
        return DownloadAppointmentPdfFailure(failure);
      },
    );
  }

  Future<void> _onSendSMS(
    SendSMS event,
    Emitter<SharedState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<String, SharedState>(
      emit: emit,
      loadingState: SendSMSLoading(),
      callUseCase: sendSMSUseCase(
        params: SendSMSParams(
          mobileNumber: event.mobileNumber,
          message: event.message,
        ),
      ),
      onSuccess: (message) => SendSMSSuccess(message),
      onFailure: (failure) => SendSMSFailure(failure),
    );
  }
}
