// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:printing/printing.dart';

import '../../../../core/config/di/dependency_injection.dart';
import '../../../../core/config/logger/app_logger.dart';
import '../../../../core/constants/analytics_events.dart';
import '../../../../core/services/analytics_service.dart';
import '../../../../core/utils/helpers/snack_bar_helper.dart';
import '../widgets/animations/animated_app_bar.dart';

///  PDF Viewer
class PdfViewerPage extends StatelessWidget {
  final Uint8List pdfBytes;
  final Function()? onDownload;
  final String title;
  const PdfViewerPage({
    super.key,
    required this.pdfBytes,
    required this.onDownload,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;

    return Scaffold(
      appBar: AnimatedAppBar(
        appBarHeight: 60,
        leading: IconButton(
          onPressed: () => context.popRoute(),
          icon: Icon(
            Icons.arrow_back_ios_new_rounded,
            color: appColors.whiteColor,
          ),
        ),
        title: title,
        style: textTheme.titleLarge?.copyWith(
          color: appColors.whiteColor,
          fontSize: 16,
        ),
        actions: [
          // _buildActionButton(
          //   context: context,
          //   // icon: isDownloading ? Icons.download_done : Icons.download,
          //   icon: Icons.download,
          //   tooltip: "Download PDF",
          //   onPressed: () => onDownload,
          // ),
          _buildActionButton(
            context: context,
            icon: Icons.share,
            tooltip: 'Share PDF',
            onPressed: () => _sharePDF(context: context),
          ),
          _buildActionButton(
            context: context,
            // icon: Icons.qr_code,
            icon: Icons.print,
            tooltip: 'Print',
            onPressed: () => _handlePDFPrint(context: context),
          ),
          const SizedBox(width: 10),
        ],
      ),
      // body: SfPdfViewer.memory(pdfBytes),
      body: PdfPreview(
        build: (format) => pdfBytes,
        useActions: false,
      ),
    );
  }

  _buildActionButton({
    required BuildContext context,
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
  }) {
    final appColors = context.appColors;
    return IconButton(
      tooltip: tooltip,
      onPressed: onPressed,
      icon: Icon(
        icon,
        color: appColors.whiteColor,
      ),
    );
  }

  /// Handle PDF Print
  Future<void> _handlePDFPrint({
    required BuildContext context,
  }) async {
    final analyticsService = sl<AnalyticsService>();

    // Determine document type based on title
    String documentType = 'Unknown';
    String eventName = 'pdf_printed';

    if (title.toLowerCase().contains('order')) {
      documentType = 'Sales Order';
      eventName = AnalyticsEvents.order_pdf_printed;
    } else if (title.toLowerCase().contains('appointment')) {
      documentType = 'Appointment';
      eventName = AnalyticsEvents.appointment_pdf_printed;
    } else if (title.toLowerCase().contains('lab') ||
        title.toLowerCase().contains('result')) {
      documentType = 'Lab Result';
      eventName = AnalyticsEvents.lab_result_pdf_printed;
    }

    // Track PDF printing initiated
    analyticsService.logEvent(
      name: eventName,
      parameters: {
        'pdf_size_bytes': pdfBytes.length.toString(),
        'document_type': documentType,
        'title': title,
        'timestamp': DateTime.now().toIso8601String(),
        'action': 'print',
        'source': 'pdf_viewer_page',
      },
    );

    try {
      await Printing.layoutPdf(
        onLayout: (format) async {
          return pdfBytes;
        },
      );

      // Track successful PDF printing
      analyticsService.logEvent(
        name: eventName,
        parameters: {
          'pdf_size_bytes': pdfBytes.length.toString(),
          'document_type': documentType,
          'title': title,
          'timestamp': DateTime.now().toIso8601String(),
          'action': 'print_success',
          'status': 'success',
          'source': 'pdf_viewer_page',
        },
      );
    } catch (e, s) {
      // Track PDF printing failure
      analyticsService.logEvent(
        name: eventName,
        parameters: {
          'pdf_size_bytes': pdfBytes.length.toString(),
          'document_type': documentType,
          'title': title,
          'error_message': e.toString(),
          'timestamp': DateTime.now().toIso8601String(),
          'action': 'print_failed',
          'status': 'failed',
          'source': 'pdf_viewer_page',
        },
      );

      AppLogger().error(
        'Error printing PDF: $e',
        error: e,
        stackTrace: s,
      );
      SnackBarHelper.showErrorSnackBar(
        context,
        message: 'Error printing PDF: $e',
      );
    }
  }

  // Function to share PDF directly without downloading
  Future<void> _sharePDF({
    required BuildContext context,
  }) async {
    final analyticsService = sl<AnalyticsService>();

    // Determine document type based on title
    String documentType = 'Unknown';
    String eventName = 'pdf_shared';

    if (title.toLowerCase().contains('order')) {
      documentType = 'Sales Order';
      eventName = AnalyticsEvents.order_pdf_shared;
    } else if (title.toLowerCase().contains('appointment')) {
      documentType = 'Appointment';
      eventName = AnalyticsEvents.appointment_pdf_shared;
    } else if (title.toLowerCase().contains('lab') ||
        title.toLowerCase().contains('result')) {
      documentType = 'Lab Result';
      eventName = AnalyticsEvents.lab_result_pdf_shared;
    }

    // Track PDF sharing initiated
    analyticsService.logEvent(
      name: eventName,
      parameters: {
        'pdf_size_bytes': pdfBytes.length.toString(),
        'document_type': documentType,
        'title': title,
        'timestamp': DateTime.now().toIso8601String(),
        'action': 'share',
        'source': 'pdf_viewer_page',
      },
    );

    try {
      final uniqueFileName =
          '${documentType.toLowerCase().replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}.pdf';

      await Printing.sharePdf(
        bytes: pdfBytes,
        filename: uniqueFileName,
        subject: '$title PDF',
      );

      // Track successful PDF sharing
      analyticsService.logEvent(
        name: eventName,
        parameters: {
          'pdf_size_bytes': pdfBytes.length.toString(),
          'document_type': documentType,
          'title': title,
          'filename': uniqueFileName,
          'timestamp': DateTime.now().toIso8601String(),
          'action': 'share_success',
          'status': 'success',
          'source': 'pdf_viewer_page',
        },
      );
    } catch (e, s) {
      // Track PDF sharing failure
      analyticsService.logEvent(
        name: eventName,
        parameters: {
          'pdf_size_bytes': pdfBytes.length.toString(),
          'document_type': documentType,
          'title': title,
          'error_message': e.toString(),
          'timestamp': DateTime.now().toIso8601String(),
          'action': 'share_failed',
          'status': 'failed',
          'source': 'pdf_viewer_page',
        },
      );

      AppLogger().error(
        'Error Sharing PDF: $e',
        error: e,
        stackTrace: s,
      );
      SnackBarHelper.showErrorSnackBar(
        context,
        message: 'Error Sharing PDF: $e',
      );
    }
  }
}
