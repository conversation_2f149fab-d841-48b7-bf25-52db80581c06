// ignore_for_file: unreachable_switch_default

import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/image_type.dart';
import 'package:hodan_hospital/core/services/image_picker_permission_service.dart';
import 'package:hodan_hospital/gen/assets.gen.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/image_place_holder.dart';
import 'package:image_picker/image_picker.dart';

class CustomImagePickerCard extends StatelessWidget {
  final Function(XFile pickedImage)? onCamera;
  final Function(XFile pickedImage)? onGallery;
  final bool isLoading;
  final XFile? imageFile;
  final String? imageUrl;
  final String? imagePath;
  final double radius;
  final bool showIcon;
  final BoxFit? boxFit;
  final ImageType imageType;
  final String? userName;
  final Color? backgroundColor;

  const CustomImagePickerCard({
    super.key,
    this.onCamera,
    this.onGallery,
    this.isLoading = true,
    this.imageFile,
    this.imageUrl,
    this.imagePath,
    this.radius = 50.0, // Default radius
    this.showIcon = false,
    this.boxFit = BoxFit.cover,
    this.imageType = ImageType.other,
    this.userName,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: GestureDetector(
        onTap: isLoading
            ? null
            : () {
                final permissionService = ImagePickerPermissionService();
                showImagePickerDialog(
                  context: context,
                  onCamera: () async {
                    context.popRoute();
                    final selectedImage =
                        await permissionService.pickFromCamera(
                      context: context,
                      maxWidth: 1024,
                      maxHeight: 1024,
                      imageQuality: 85,
                    );
                    if (selectedImage != null) {
                      onCamera?.call(selectedImage);
                    }
                  },
                  onGallery: () async {
                    context.popRoute();
                    final selectedImage =
                        await permissionService.pickFromGallery(
                      context: context,
                      maxWidth: 1024,
                      maxHeight: 1024,
                      imageQuality: 85,
                    );
                    if (selectedImage != null) {
                      onGallery?.call(selectedImage);
                    }
                  },
                );
              },
        child: CircleAvatar(
          backgroundColor: backgroundColor ?? context.appColors.disabledColor,
          radius: radius.r, // Use the customizable radius
          child: Stack(
            children: [
              _buildImageContent(context), // Handle image priority logic
              if (showIcon) _buildAddPhotoIcon(), // Conditionally show the icon
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageContent(BuildContext context) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 2000),
      reverseDuration: const Duration(milliseconds: 4000),
      transitionBuilder: (child, animation) {
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(
            scale: animation,
            child: child,
          ),
        );
      },
      child: _buildImageWidget(context),
    );
  }

  Widget _buildImageWidget(BuildContext context) {
    if (imageFile != null) {
      return ClipOval(
        key: ValueKey('file_${imageFile!.path}'),
        child: SizedBox(
          width: radius * 2.w,
          height: radius * 2.h,
          child: Image.file(
            File(imageFile!.path),
            fit: boxFit,
          ),
        ),
      );
    } else if (imageUrl != null && imageUrl!.isNotEmpty) {
      return ClipOval(
        key: ValueKey('url_$imageUrl'),
        child: SizedBox(
          width: radius * 2.w,
          height: radius * 2.h,
          child: CachedNetworkImage(
            imageUrl: imageUrl!,
            placeholder: (context, url) => const ImagePlaceholder(),
            errorWidget: (context, url, error) {
              if (imageType == ImageType.doctor) {
                return Image.asset(
                  Assets.images.png.doctor.path,
                  fit: boxFit,
                );
              }
              // if (_shouldShowInitials()) {
              //   return _buildInitials(context);
              // }
              if (imageType == ImageType.profile) {
                return Image.network(
                  // Assets.images.png.doctor.path,
                  'https://cdn-icons-png.flaticon.com/128/149/149071.png',
                  fit: boxFit,
                );
              }

              return Icon(
                Icons.camera_alt,
                size: radius.w,
                color: context.appColors.whiteColor,
              );
            },
            fit: boxFit,
          ),
        ),
      );
    } else if (imagePath != null && imagePath!.isNotEmpty) {
      return ClipOval(
        key: ValueKey('path_$imagePath'),
        child: SizedBox(
          width: radius * 2.w,
          height: radius * 2.h,
          child: Image.asset(
            imagePath!,
            fit: boxFit,
          ),
        ),
      );
    } else {
      if (imageType == ImageType.doctor) {
        return Image.asset(
          Assets.images.png.doctor.path,
          fit: boxFit,
          key: ValueKey('asset_${Assets.images.png.doctor.path}'),
        );
      }
      // if (_shouldShowInitials()) {
      //   return _buildInitials(context);
      // }
      if (imageType == ImageType.profile) {
        return Image.network(
          // Assets.images.png.doctor.path,
          'https://cdn-icons-png.flaticon.com/128/149/149071.png',
          fit: boxFit,
        );
      }
      return Icon(
        Icons.image,
        size: radius.w,
        color: context.appColors.whiteColor,
        key: const ValueKey('default_icon'),
      );
    }
  }

  // Helper method to build the "Add Photo" icon
  Widget _buildAddPhotoIcon() {
    return Positioned(
      right: 3,
      bottom: 1,
      child: Container(
        padding: EdgeInsets.all(5.w),
        decoration: const BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              // color: Colors.black,
              spreadRadius: 1,
              blurRadius: 1,
            ),
          ],
        ),
        child: Icon(
          Icons.add_a_photo,
          color: Colors.grey.shade900,
        ),
      ),
    );
  }

  void showImagePickerDialog({
    required BuildContext context,
    required Function() onGallery,
    required Function() onCamera,
  }) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog.adaptive(
          title: const Text('Select Image'),
          content: Material(
            color: Colors.transparent,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 10.h),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildImagePickerOption(
                        context: context,
                        icon: Icons.photo_camera,
                        label: 'Camera',
                        onTap: onCamera,
                      ),
                      SizedBox(width: 30.w),
                      _buildImagePickerOption(
                        context: context,
                        icon: Icons.photo,
                        label: 'Gallery',
                        onTap: onGallery,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildImagePickerOption({
    required BuildContext context,
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: Theme.of(context).dividerColor.withValues(alpha: 0.3),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 40.w,
              color: Theme.of(context).primaryColor,
            ),
            SizedBox(height: 8.h),
            Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  // bool _shouldShowInitials() {
  //   return imageType == ImageType.profile &&
  //       userName != null &&
  //       userName!.trim().isNotEmpty;
  // }

  // Widget _buildInitials(BuildContext context) {
  //   final appColors = context.appColors;
  //   final initials = _getInitials(userName ?? '');

  //   return Container(
  //     width: radius * 2.w,
  //     height: radius * 2.h,
  //     decoration: BoxDecoration(
  //       color: appColors.subtextColor.withValues(alpha: 0.1),
  //       shape: BoxShape.circle,
  //     ),
  //     child: Center(
  //       child: Text(
  //         initials,
  //         style: TextStyle(
  //           fontSize: radius.w * 0.8,
  //           color: appColors.whiteColor,
  //           fontWeight: FontWeight.bold,
  //         ),
  //       ),
  //     ),
  //   );
  // }

  // String _getInitials(String name) {
  //   final words = name.trim().split(' ');
  //   if (words.length == 1) {
  //     return words[0][0].toUpperCase();
  //   } else {
  //     return (words[0][0] + words[1][0]).toUpperCase();
  //   }
  // }
}
