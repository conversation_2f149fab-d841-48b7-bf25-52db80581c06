PODS:
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - Firebase/CoreOnly (12.0.0):
    - FirebaseCore (~> 12.0.0)
  - Firebase/Crashlytics (12.0.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 12.0.0)
  - Firebase/Messaging (12.0.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 12.0.0)
  - firebase_analytics (12.0.0):
    - firebase_core
    - FirebaseAnalytics (= 12.0.0)
    - Flutter
  - firebase_core (4.0.0):
    - Firebase/CoreOnly (= 12.0.0)
    - Flutter
  - firebase_crashlytics (5.0.0):
    - Firebase/Crashlytics (= 12.0.0)
    - firebase_core
    - Flutter
  - firebase_messaging (16.0.0):
    - Firebase/Messaging (= 12.0.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics (12.0.0):
    - FirebaseAnalytics/Default (= 12.0.0)
    - FirebaseCore (~> 12.0.0)
    - FirebaseInstallations (~> 12.0.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/Default (12.0.0):
    - FirebaseCore (~> 12.0.0)
    - FirebaseInstallations (~> 12.0.0)
    - GoogleAppMeasurement/Default (= 12.0.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseCore (12.0.0):
    - FirebaseCoreInternal (~> 12.0.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreExtension (12.0.0):
    - FirebaseCore (~> 12.0.0)
  - FirebaseCoreInternal (12.0.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseCrashlytics (12.0.0):
    - FirebaseCore (~> 12.0.0)
    - FirebaseInstallations (~> 12.0.0)
    - FirebaseRemoteConfigInterop (~> 12.0.0)
    - FirebaseSessions (~> 12.0.0)
    - GoogleDataTransport (~> 10.1)
    - GoogleUtilities/Environment (~> 8.1)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInstallations (12.0.0):
    - FirebaseCore (~> 12.0.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (12.0.0):
    - FirebaseCore (~> 12.0.0)
    - FirebaseInstallations (~> 12.0.0)
    - GoogleDataTransport (~> 10.1)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Reachability (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfigInterop (12.0.0)
  - FirebaseSessions (12.0.0):
    - FirebaseCore (~> 12.0.0)
    - FirebaseCoreExtension (~> 12.0.0)
    - FirebaseInstallations (~> 12.0.0)
    - GoogleDataTransport (~> 10.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - Flutter (1.0.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
  - GoogleAdsOnDeviceConversion (2.1.0):
    - GoogleUtilities/Logger (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/Core (12.0.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/Default (12.0.0):
    - GoogleAdsOnDeviceConversion (= 2.1.0)
    - GoogleAppMeasurement/Core (= 12.0.0)
    - GoogleAppMeasurement/IdentitySupport (= 12.0.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/IdentitySupport (12.0.0):
    - GoogleAppMeasurement/Core (= 12.0.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMLKit/BarcodeScanning (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitBarcodeScanning (~> 6.0.0)
  - GoogleMLKit/MLKitCore (7.0.0):
    - MLKitCommon (~> 12.0.0)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMSessionFetcher/Core (3.5.0)
  - image_picker_ios (0.0.1):
    - Flutter
  - in_app_review (2.0.0):
    - Flutter
  - MLImage (1.0.0-beta6)
  - MLKitBarcodeScanning (6.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitCommon (12.0.0):
    - GoogleDataTransport (~> 10.0)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/Logger (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitVision (8.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta6)
    - MLKitCommon (~> 12.0)
  - mobile_scanner (6.0.2):
    - Flutter
    - GoogleMLKit/BarcodeScanning (~> 7.0.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - printing (1.0.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - in_app_review (from `.symlinks/plugins/in_app_review/ios`)
  - mobile_scanner (from `.symlinks/plugins/mobile_scanner/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - printing (from `.symlinks/plugins/printing/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - GoogleAdsOnDeviceConversion
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - GTMSessionFetcher
    - MLImage
    - MLKitBarcodeScanning
    - MLKitCommon
    - MLKitVision
    - nanopb
    - PromisesObjC
    - PromisesSwift

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  in_app_review:
    :path: ".symlinks/plugins/in_app_review/ios"
  mobile_scanner:
    :path: ".symlinks/plugins/mobile_scanner/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  printing:
    :path: ".symlinks/plugins/printing/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  Firebase: 800d487043c0557d9faed71477a38d9aafb08a41
  firebase_analytics: cd56fc56f75c1df30a6ff5290cd56e230996a76d
  firebase_core: 633e1851ffe1b9ab875f6467a4f574c79cef02e4
  firebase_crashlytics: 2c6c1a17900a38081d938330e9f48e60ec5b255d
  firebase_messaging: d17feef781edc84ebefe62624fb384358ad96361
  FirebaseAnalytics: 6d790cd1b159b4eb61a99948df0934ce505a34f7
  FirebaseCore: 055f4ab117d5964158c833f3d5e7ec6d91648d4a
  FirebaseCoreExtension: 639afb3de6abd611952be78a794c54a47fa0f361
  FirebaseCoreInternal: dedc28e569a4be85f38f3d6af1070a2e12018d55
  FirebaseCrashlytics: db75aa0cab8d00f68406fa247c32fe17ade884d7
  FirebaseInstallations: d4c7c958f99c8860d7fcece786314ae790e2f988
  FirebaseMessaging: af49f8d7c0a3d2a017d9302c80946f45a7777dde
  FirebaseRemoteConfigInterop: bfa0ea72ba3dc5af739777296424e46bd6f42613
  FirebaseSessions: 4e784acda213108aafef536535cdfc03504acc42
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_local_notifications: a5a732f069baa862e728d839dd2ebb904737effb
  flutter_native_splash: c32d145d68aeda5502d5f543ee38c192065986cf
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  fluttertoast: 2c67e14dce98bbdb200df9e1acf610d7a6264ea1
  GoogleAdsOnDeviceConversion: 2be6297a4f048459e0ae17fad9bfd2844e10cf64
  GoogleAppMeasurement: 8f6ab04ad6ae493b53fcf56bd26323fb2f1384f3
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMLKit: eff9e23ec1d90ea4157a1ee2e32a4f610c5b3318
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  in_app_review: 5596fe56fab799e8edb3561c03d053363ab13457
  MLImage: 0ad1c5f50edd027672d8b26b0fee78a8b4a0fc56
  MLKitBarcodeScanning: 0a3064da0a7f49ac24ceb3cb46a5bc67496facd2
  MLKitCommon: 07c2c33ae5640e5380beaaa6e4b9c249a205542d
  MLKitVision: 45e79d68845a2de77e2dd4d7f07947f0ed157b0e
  mobile_scanner: af8f71879eaba2bbcb4d86c6a462c3c0e7f23036
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  printing: 54ff03f28fe9ba3aa93358afb80a8595a071dd07
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d

PODFILE CHECKSUM: afba00b7b97b81b5c81bacaa07af76ee9721251a

COCOAPODS: 1.16.2
