# 📘 Developer Notes — Hodan Hospital App

This document contains useful setup, maintenance, and deployment commands used during development.

---

## ⚙️ Project Setup

### Initial Setup
```bash
flutter clean
flutter pub get
```

### iOS Setup
```bash
cd ios
rm -rf Pods Podfile.lock Runner.xcworkspace
pod deintegrate
pod install --repo-update
cd ..
```

Then run the app:
```bash
flutter run
```

---

## 🔐 Firebase Setup

- Firebase is initialized using `firebase_core` and configured via `firebase_options.dart`.
- Services used:
  - `firebase_analytics`
  - `firebase_crashlytics`
  - `firebase_messaging`

Initialization happens in `main()` before `runApp()`.

---

## 🔔 Push Notifications

```dart
await notificationService.initialize();
await notificationService.requestPermissions();
final token = await notificationService.getToken();
```

- Token is logged with `AppLogger`.
- Make sure permissions are requested on:
  - iOS (notification prompt)
  - Android 13+ (POST_NOTIFICATIONS permission)

---

## 📊 Analytics

- Custom wrapper `AnalyticsService` is used.
- Supports:
  - `logEvent()`
  - `logLogin()`
  - `logRegister()`
  - `logScreenView()`
  - `logPaymentInfo()`
- Parameters are sanitized for Firebase compatibility.
- Soft errors are tracked using `logError()`.

---

## 💥 Crashlytics

- Wrapped in `CrashlyticsService`.
- Tracks:
  - Non-fatal errors via `logError()`
  - Flutter fatal errors via `recordFlutterFatalError()`
- Captures:
  - `FlutterError.onError`
  - `PlatformDispatcher.instance.onError`
  - `ErrorWidget.builder`
- Skips logging in `kDebugMode`.

---

## 🧪 Testing Crashlytics

Trigger a test crash:
```dart
FirebaseCrashlytics.instance.crash();
```

Trigger a test Dart exception:
```dart
throw Exception('Test crash for Crashlytics');
```

---

## 🐦 Shorebird Commands

### 🔧 Create Patch Release
```bash
shorebird patch --platforms=android
# Or with explicit version:
shorebird patch --platforms=android --release-version=1.0.3+6

Note: without the --release-version option will patch the current version of the app.

```

### 🧪 Patch for Staging
```bash
shorebird patch android --track=staging
```

### 👀 Preview a Release
```bash
shorebird preview \
  --staging \
  --app-id d54a87ef-28ef-4d2c-9eb8-b21ae422df98 \
  --release-version 1.0.3+7
```

### 🚀 Promote to Stable
```bash
shorebird promote --track=staging --to=stable
```

---

## 🔒 Privacy Policy

> https://www.freeprivacypolicy.com/live/************************************

---

## 📂 Project Notes

- `lib/core/services/`: Analytics, Crashlytics, Notifications, SecureStorage
- `lib/core/config/`: Logger, DI, Env
- `lib/features/shared/presentation/pages/`: Error UI (`ModernErrorPage`)
- Logging is handled by `AppLogger` (console and file support).

---

Happy coding! 💙
