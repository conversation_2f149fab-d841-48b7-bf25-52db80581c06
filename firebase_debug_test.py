#!/usr/bin/env python3
"""
Firebase Admin SDK Debug Test Script
Run this script to diagnose Firebase Admin SDK issues
"""

import sys
import os

def test_firebase_admin_import():
    """Test Firebase Admin SDK import and check available methods"""
    print("=" * 60)
    print("Firebase Admin SDK Debug Test")
    print("=" * 60)
    
    try:
        print("1. Testing Firebase Admin SDK import...")
        import firebase_admin
        print(f"✅ Firebase Admin SDK imported successfully")
        print(f"   Version: {getattr(firebase_admin, '__version__', 'Unknown')}")
        print(f"   Location: {firebase_admin.__file__}")
        
    except ImportError as e:
        print(f"❌ Failed to import firebase_admin: {e}")
        return False
    
    try:
        print("\n2. Testing messaging module import...")
        from firebase_admin import messaging
        print(f"✅ Messaging module imported successfully")
        print(f"   Location: {messaging.__file__}")
        
    except ImportError as e:
        print(f"❌ Failed to import messaging: {e}")
        return False
    
    print("\n3. Checking available messaging methods...")
    messaging_methods = [attr for attr in dir(messaging) if not attr.startswith('_')]
    print(f"   Available methods ({len(messaging_methods)}): {', '.join(messaging_methods[:10])}...")
    
    print("\n4. Checking specific methods...")
    methods_to_check = [
        'send_multicast',
        'MulticastMessage', 
        'send',
        'Message',
        'Notification',
        'subscribe_to_topic',
        'unsubscribe_from_topic'
    ]
    
    for method in methods_to_check:
        has_method = hasattr(messaging, method)
        status = "✅" if has_method else "❌"
        print(f"   {status} {method}: {'Available' if has_method else 'Not Available'}")
    
    print("\n5. Python environment info...")
    print(f"   Python version: {sys.version}")
    print(f"   Python executable: {sys.executable}")
    print(f"   Python path: {sys.path[:3]}...")  # Show first 3 paths
    
    print("\n6. Checking for multiple firebase-admin installations...")
    try:
        import pkg_resources
        firebase_packages = [pkg for pkg in pkg_resources.working_set if 'firebase' in pkg.project_name.lower()]
        if firebase_packages:
            print("   Firebase-related packages found:")
            for pkg in firebase_packages:
                print(f"     - {pkg.project_name}: {pkg.version} at {pkg.location}")
        else:
            print("   No firebase packages found via pkg_resources")
    except ImportError:
        print("   pkg_resources not available")
    
    # Alternative method using pip
    try:
        import subprocess
        result = subprocess.run([sys.executable, '-m', 'pip', 'list'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            firebase_lines = [line for line in result.stdout.split('\n') 
                            if 'firebase' in line.lower()]
            if firebase_lines:
                print("   Firebase packages via pip list:")
                for line in firebase_lines:
                    print(f"     - {line}")
    except Exception as e:
        print(f"   Could not check pip list: {e}")
    
    print("\n7. Testing basic Firebase functionality...")
    try:
        # Test creating a notification object
        notification = messaging.Notification(
            title="Test",
            body="Test message"
        )
        print("✅ Notification object created successfully")
        
        # Test creating a Message object
        message_obj = messaging.Message(
            notification=notification,
            token="test_token"
        )
        print("✅ Message object created successfully")
        
        # Test MulticastMessage if available
        if hasattr(messaging, 'MulticastMessage'):
            multicast_msg = messaging.MulticastMessage(
                notification=notification,
                tokens=["test_token1", "test_token2"]
            )
            print("✅ MulticastMessage object created successfully")
        else:
            print("❌ MulticastMessage not available")
            
    except Exception as e:
        print(f"❌ Error testing Firebase objects: {e}")
    
    print("\n" + "=" * 60)
    print("Debug test completed!")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    test_firebase_admin_import()
